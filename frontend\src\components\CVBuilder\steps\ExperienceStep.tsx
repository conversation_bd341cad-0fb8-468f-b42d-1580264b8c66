import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { FormStepProps, Experience } from '../../../types';
import { generateId } from '../../../utils';
import { Briefcase, Plus, Trash2, Edit } from 'lucide-react';

const ExperienceStep: React.FC<FormStepProps> = ({ data, updateData, onNext, onPrevious }) => {
  const [experienceList, setExperienceList] = useState<Experience[]>(data.experience || []);
  const [editingIndex, setEditingIndex] = useState<number | null>(null);
  const [showForm, setShowForm] = useState(false);

  const {
    register,
    handleSubmit,
    reset,
    watch,
    formState: { errors },
  } = useForm<Experience>();

  const isCurrentJob = watch('end_date') === 'current';

  const onSubmit = (formData: Experience) => {
    const newExperience = { 
      ...formData, 
      id: generateId(),
      end_date: formData.end_date === 'current' ? 'Present' : formData.end_date
    };
    
    if (editingIndex !== null) {
      const updated = [...experienceList];
      updated[editingIndex] = newExperience;
      setExperienceList(updated);
      setEditingIndex(null);
    } else {
      setExperienceList([...experienceList, newExperience]);
    }
    
    reset();
    setShowForm(false);
  };

  const handleEdit = (index: number) => {
    const experience = experienceList[index];
    const editData = {
      ...experience,
      end_date: experience.end_date === 'Present' ? 'current' : experience.end_date
    };
    reset(editData);
    setEditingIndex(index);
    setShowForm(true);
  };

  const handleDelete = (index: number) => {
    const updated = experienceList.filter((_, i) => i !== index);
    setExperienceList(updated);
  };

  const handleNext = () => {
    updateData('experience', experienceList);
    onNext();
  };

  const cancelEdit = () => {
    reset();
    setEditingIndex(null);
    setShowForm(false);
  };

  return (
    <div>
      <div className="flex items-center mb-6">
        <Briefcase className="h-6 w-6 text-primary-600 mr-3" />
        <div>
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
            Work Experience
          </h2>
          <p className="text-gray-600 dark:text-gray-400">
            Add your professional work experience and achievements
          </p>
        </div>
      </div>

      {/* Experience List */}
      {experienceList.length > 0 && (
        <div className="mb-6 space-y-4">
          {experienceList.map((experience, index) => (
            <div key={experience.id} className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
              <div className="flex justify-between items-start">
                <div className="flex-1">
                  <h3 className="font-semibold text-gray-900 dark:text-white">
                    {experience.position}
                  </h3>
                  <p className="text-gray-600 dark:text-gray-300">
                    {experience.company}
                  </p>
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    {experience.start_date} - {experience.end_date}
                  </p>
                  {experience.location && (
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                      {experience.location}
                    </p>
                  )}
                  {experience.description && (
                    <p className="mt-2 text-sm text-gray-600 dark:text-gray-300">
                      {experience.description}
                    </p>
                  )}
                </div>
                <div className="flex space-x-2 ml-4">
                  <button
                    onClick={() => handleEdit(index)}
                    className="p-2 text-gray-500 hover:text-primary-600"
                  >
                    <Edit className="h-4 w-4" />
                  </button>
                  <button
                    onClick={() => handleDelete(index)}
                    className="p-2 text-gray-500 hover:text-red-600"
                  >
                    <Trash2 className="h-4 w-4" />
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Add Experience Button */}
      {!showForm && (
        <button
          onClick={() => setShowForm(true)}
          className="mb-6 inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600"
        >
          <Plus className="h-4 w-4 mr-2" />
          Add Experience
        </button>
      )}

      {/* Experience Form */}
      {showForm && (
        <form onSubmit={handleSubmit(onSubmit)} className="mb-6 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-6">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
            {editingIndex !== null ? 'Edit Experience' : 'Add Experience'}
          </h3>
          
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Job Title *
              </label>
              <input
                {...register('position', { required: 'Job title is required' })}
                type="text"
                className="mt-1 input"
                placeholder="e.g., Senior Software Engineer"
              />
              {errors.position && (
                <p className="mt-1 text-sm text-red-600 dark:text-red-400">
                  {errors.position.message}
                </p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Company *
              </label>
              <input
                {...register('company', { required: 'Company is required' })}
                type="text"
                className="mt-1 input"
                placeholder="e.g., Tech Solutions Inc."
              />
              {errors.company && (
                <p className="mt-1 text-sm text-red-600 dark:text-red-400">
                  {errors.company.message}
                </p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Location
              </label>
              <input
                {...register('location')}
                type="text"
                className="mt-1 input"
                placeholder="e.g., New York, NY"
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  Start Date *
                </label>
                <input
                  {...register('start_date', { required: 'Start date is required' })}
                  type="month"
                  className="mt-1 input"
                />
                {errors.start_date && (
                  <p className="mt-1 text-sm text-red-600 dark:text-red-400">
                    {errors.start_date.message}
                  </p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  End Date *
                </label>
                <select
                  {...register('end_date', { required: 'End date is required' })}
                  className="mt-1 input"
                >
                  <option value="">Select end date</option>
                  <option value="current">Current Position</option>
                  {/* Generate month options for the last 10 years */}
                  {Array.from({ length: 120 }, (_, i) => {
                    const date = new Date();
                    date.setMonth(date.getMonth() - i);
                    const value = date.toISOString().slice(0, 7);
                    const label = date.toLocaleDateString('en-US', { year: 'numeric', month: 'long' });
                    return (
                      <option key={value} value={value}>
                        {label}
                      </option>
                    );
                  })}
                </select>
                {errors.end_date && (
                  <p className="mt-1 text-sm text-red-600 dark:text-red-400">
                    {errors.end_date.message}
                  </p>
                )}
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Description *
              </label>
              <textarea
                {...register('description', { required: 'Description is required' })}
                rows={4}
                className="mt-1 textarea"
                placeholder="Describe your responsibilities, achievements, and key contributions..."
              />
              {errors.description && (
                <p className="mt-1 text-sm text-red-600 dark:text-red-400">
                  {errors.description.message}
                </p>
              )}
              <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                Tip: Use bullet points and quantify your achievements where possible
              </p>
            </div>
          </div>

          <div className="flex justify-end space-x-3 mt-6">
            <button type="button" onClick={cancelEdit} className="btn-outline">
              Cancel
            </button>
            <button type="submit" className="btn-primary">
              {editingIndex !== null ? 'Update' : 'Add'} Experience
            </button>
          </div>
        </form>
      )}

      {/* Navigation */}
      <div className="flex justify-between">
        <button type="button" onClick={onPrevious} className="btn-outline">
          Previous
        </button>
        <button type="button" onClick={handleNext} className="btn-primary">
          Next: Skills
        </button>
      </div>
    </div>
  );
};

export default ExperienceStep;
