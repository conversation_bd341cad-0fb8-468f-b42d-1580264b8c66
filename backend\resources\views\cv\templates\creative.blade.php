<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ $cv->title }}</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            box-shadow: 0 0 30px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 40px;
            text-align: center;
            position: relative;
        }
        
        .header::before {
            content: '';
            position: absolute;
            bottom: -20px;
            left: 50%;
            transform: translateX(-50%);
            width: 0;
            height: 0;
            border-left: 20px solid transparent;
            border-right: 20px solid transparent;
            border-top: 20px solid #764ba2;
        }
        
        .header h1 {
            font-size: 2.8em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .header .title {
            font-size: 1.3em;
            margin-bottom: 20px;
            opacity: 0.9;
        }
        
        .contact-info {
            display: flex;
            justify-content: center;
            gap: 25px;
            flex-wrap: wrap;
            font-size: 0.95em;
        }
        
        .contact-info span {
            background: rgba(255,255,255,0.2);
            padding: 8px 15px;
            border-radius: 20px;
            /* backdrop-filter: blur(10px); - Removed for better PDF compatibility */
        }
        
        .content {
            padding: 40px;
        }
        
        .section {
            margin-bottom: 35px;
        }
        
        .section-title {
            font-size: 1.5em;
            color: #667eea;
            margin-bottom: 20px;
            position: relative;
            padding-left: 25px;
        }
        
        .section-title::before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 15px;
            height: 15px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 50%;
        }
        
        .summary {
            font-style: italic;
            color: #555;
            text-align: justify;
            background: #f8f9ff;
            padding: 20px;
            border-radius: 10px;
            border-left: 4px solid #667eea;
        }
        
        .experience-item, .education-item, .project-item {
            margin-bottom: 25px;
            padding: 20px;
            background: #f8f9ff;
            border-radius: 10px;
            border-left: 4px solid #764ba2;
            position: relative;
        }
        
        .item-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 10px;
            flex-wrap: wrap;
        }
        
        .item-title {
            font-weight: bold;
            color: #667eea;
            font-size: 1.1em;
        }
        
        .item-company {
            color: #764ba2;
            font-style: italic;
            margin-top: 3px;
        }
        
        .item-date {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 5px 12px;
            border-radius: 15px;
            font-size: 0.85em;
            white-space: nowrap;
        }
        
        .item-description {
            margin-top: 10px;
            color: #555;
            text-align: justify;
        }
        
        .skills-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
        }
        
        .skill-category {
            background: linear-gradient(135deg, #f8f9ff 0%, #e8ecff 100%);
            padding: 20px;
            border-radius: 15px;
            border: 2px solid #e0e6ff;
            transition: transform 0.3s ease;
        }
        
        .skill-category:hover {
            transform: translateY(-5px);
        }
        
        .skill-category h4 {
            color: #667eea;
            margin-bottom: 12px;
            font-size: 1.1em;
        }
        
        .skill-list {
            list-style: none;
        }
        
        .skill-list li {
            padding: 4px 0;
            color: #555;
            position: relative;
            padding-left: 15px;
        }
        
        .skill-list li::before {
            content: '▶';
            position: absolute;
            left: 0;
            color: #764ba2;
            font-size: 0.8em;
        }
        
        .languages, .hobbies {
            display: flex;
            flex-wrap: wrap;
            gap: 12px;
        }
        
        .language-item, .hobby-item {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 8px 16px;
            border-radius: 25px;
            font-size: 0.9em;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
            transition: transform 0.3s ease;
        }
        
        .language-item:hover, .hobby-item:hover {
            transform: translateY(-2px);
        }
        
        .two-column {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
        }
        
        @media (max-width: 768px) {
            .two-column {
                grid-template-columns: 1fr;
            }
            
            .item-header {
                flex-direction: column;
                align-items: flex-start;
            }
            
            .item-date {
                margin-top: 5px;
            }
        }
        
        @media print {
            body {
                background: white !important;
                min-height: auto;
            }

            .container {
                box-shadow: none !important;
                margin: 0;
                max-width: none;
            }

            .content {
                padding: 20px;
            }

            .header {
                background: #667eea !important; /* Fallback solid color for gradients */
            }

            .header::before {
                display: none; /* Remove decorative elements that might cause issues */
            }

            .section-title::before {
                background: #667eea !important; /* Fallback solid color */
            }

            .item-date {
                background: #667eea !important; /* Fallback solid color */
            }

            .language-item, .hobby-item {
                background: #667eea !important; /* Fallback solid color */
                box-shadow: none !important;
            }

            .skill-category {
                background: #f8f9ff !important; /* Fallback solid color */
                transform: none !important;
                transition: none !important;
            }

            .skill-category:hover {
                transform: none !important;
            }

            .language-item:hover, .hobby-item:hover {
                transform: none !important;
            }

            /* Ensure proper page breaks */
            .section {
                page-break-inside: avoid;
            }

            .experience-item, .education-item, .project-item {
                page-break-inside: avoid;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <h1>{{ $cv->cv_data['personal']['name'] ?? 'Name' }}</h1>
            <div class="title">{{ $cv->cv_data['personal']['title'] ?? 'Professional Title' }}</div>
            <div class="contact-info">
                @if(isset($cv->cv_data['contact']['email']))
                    <span>📧 {{ $cv->cv_data['contact']['email'] }}</span>
                @endif
                @if(isset($cv->cv_data['contact']['phone']))
                    <span>📱 {{ $cv->cv_data['contact']['phone'] }}</span>
                @endif
                @if(isset($cv->cv_data['contact']['location']))
                    <span>📍 {{ $cv->cv_data['contact']['location'] }}</span>
                @endif
                @if(isset($cv->cv_data['contact']['linkedin']))
                    <span>💼 {{ $cv->cv_data['contact']['linkedin'] }}</span>
                @endif
            </div>
        </div>

        <div class="content">
            <!-- Summary -->
            @if(isset($cv->cv_data['personal']['summary']))
            <div class="section">
                <h2 class="section-title">About Me</h2>
                <p class="summary">{{ $cv->cv_data['personal']['summary'] }}</p>
            </div>
            @endif

            <!-- Experience -->
            @if(isset($cv->cv_data['experience']) && count($cv->cv_data['experience']) > 0)
            <div class="section">
                <h2 class="section-title">Work Experience</h2>
                @foreach($cv->cv_data['experience'] as $exp)
                <div class="experience-item">
                    <div class="item-header">
                        <div>
                            <div class="item-title">{{ $exp['position'] ?? 'Position' }}</div>
                            <div class="item-company">{{ $exp['company'] ?? 'Company' }}</div>
                        </div>
                        <div class="item-date">{{ $exp['start_date'] ?? '' }} - {{ $exp['end_date'] ?? 'Present' }}</div>
                    </div>
                    @if(isset($exp['description']))
                    <div class="item-description">{{ $exp['description'] }}</div>
                    @endif
                </div>
                @endforeach
            </div>
            @endif

            <!-- Education -->
            @if(isset($cv->cv_data['education']) && count($cv->cv_data['education']) > 0)
            <div class="section">
                <h2 class="section-title">Education</h2>
                @foreach($cv->cv_data['education'] as $edu)
                <div class="education-item">
                    <div class="item-header">
                        <div>
                            <div class="item-title">{{ $edu['degree'] ?? 'Degree' }}</div>
                            <div class="item-company">{{ $edu['institution'] ?? 'Institution' }}</div>
                        </div>
                        <div class="item-date">{{ $edu['start_date'] ?? '' }} - {{ $edu['end_date'] ?? 'Present' }}</div>
                    </div>
                    @if(isset($edu['description']))
                    <div class="item-description">{{ $edu['description'] }}</div>
                    @endif
                </div>
                @endforeach
            </div>
            @endif

            <!-- Skills -->
            @if(isset($cv->cv_data['skills']) && count($cv->cv_data['skills']) > 0)
            <div class="section">
                <h2 class="section-title">Skills & Expertise</h2>
                <div class="skills-grid">
                    @foreach($cv->cv_data['skills'] as $skillCategory => $skills)
                    <div class="skill-category">
                        <h4>{{ ucfirst($skillCategory) }}</h4>
                        <ul class="skill-list">
                            @if(is_array($skills))
                                @foreach($skills as $skill)
                                <li>{{ $skill }}</li>
                                @endforeach
                            @else
                                <li>{{ $skills }}</li>
                            @endif
                        </ul>
                    </div>
                    @endforeach
                </div>
            </div>
            @endif

            <!-- Projects -->
            @if(isset($cv->cv_data['projects']) && count($cv->cv_data['projects']) > 0)
            <div class="section">
                <h2 class="section-title">Featured Projects</h2>
                @foreach($cv->cv_data['projects'] as $project)
                <div class="project-item">
                    <div class="item-header">
                        <div class="item-title">{{ $project['name'] ?? 'Project Name' }}</div>
                        @if(isset($project['date']))
                        <div class="item-date">{{ $project['date'] }}</div>
                        @endif
                    </div>
                    @if(isset($project['description']))
                    <div class="item-description">{{ $project['description'] }}</div>
                    @endif
                    @if(isset($project['technologies']))
                    <div class="item-description"><strong>Tech Stack:</strong> {{ $project['technologies'] }}</div>
                    @endif
                </div>
                @endforeach
            </div>
            @endif

            <div class="two-column">
                <!-- Languages -->
                @if(isset($cv->cv_data['languages']) && count($cv->cv_data['languages']) > 0)
                <div class="section">
                    <h2 class="section-title">Languages</h2>
                    <div class="languages">
                        @foreach($cv->cv_data['languages'] as $language)
                        <span class="language-item">
                            {{ $language['name'] ?? 'Language' }}
                            @if(isset($language['level']))
                                - {{ $language['level'] }}
                            @endif
                        </span>
                        @endforeach
                    </div>
                </div>
                @endif

                <!-- Hobbies -->
                @if(isset($cv->cv_data['hobbies']) && count($cv->cv_data['hobbies']) > 0)
                <div class="section">
                    <h2 class="section-title">Interests</h2>
                    <div class="hobbies">
                        @foreach($cv->cv_data['hobbies'] as $hobby)
                        <span class="hobby-item">{{ $hobby }}</span>
                        @endforeach
                    </div>
                </div>
                @endif
            </div>
        </div>
    </div>
</body>
</html>
