<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\CV;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;
use Barryvdh\DomPDF\Facade\Pdf;

class CVController extends Controller
{
    /**
     * Validate and normalize CV data structure
     */
    private function validateAndNormalizeCVData(array $cvData): array
    {
        $requiredKeys = ['personal', 'contact', 'skills', 'experience', 'education', 'projects', 'languages', 'hobbies'];

        foreach ($requiredKeys as $key) {
            if (!isset($cvData[$key])) {
                $cvData[$key] = [];
            }
        }

        // Ensure personal data has required fields
        if (!isset($cvData['personal']['name'])) {
            $cvData['personal']['name'] = '';
        }
        if (!isset($cvData['personal']['title'])) {
            $cvData['personal']['title'] = '';
        }
        if (!isset($cvData['personal']['summary'])) {
            $cvData['personal']['summary'] = '';
        }

        // Ensure contact data is properly structured
        if (!is_array($cvData['contact'])) {
            $cvData['contact'] = [];
        }

        // Ensure arrays are properly structured
        foreach (['experience', 'education', 'projects', 'languages', 'hobbies'] as $arrayKey) {
            if (!is_array($cvData[$arrayKey])) {
                $cvData[$arrayKey] = [];
            }
        }

        // Ensure skills is properly structured (can be array or object)
        if (!isset($cvData['skills']) || (!is_array($cvData['skills']) && !is_object($cvData['skills']))) {
            $cvData['skills'] = [];
        }

        return $cvData;
    }

    /**
     * Validate template type and file existence
     */
    private function validateTemplate(string $templateType): bool
    {
        // Check if template type is valid
        if (!in_array($templateType, ['modern', 'classic', 'creative'])) {
            return false;
        }

        // Check if template file exists
        $templatePath = resource_path("views/cv/templates/{$templateType}.blade.php");
        return file_exists($templatePath) && is_readable($templatePath);
    }

    /**
     * Display a listing of the user's CVs.
     */
    public function index(Request $request): JsonResponse
    {
        $cvs = $request->user()->cvs()->orderBy('created_at', 'desc')->get();

        return response()->json([
            'success' => true,
            'data' => [
                'cvs' => $cvs
            ]
        ]);
    }

    /**
     * Store a newly created CV in storage.
     */
    public function store(Request $request): JsonResponse
    {
        // Log incoming request for debugging
        Log::info('CV Creation Request', [
            'title' => $request->title,
            'template_type' => $request->template_type,
            'has_cv_data' => !empty($request->cv_data),
            'all_data' => $request->all()
        ]);

        $validator = Validator::make($request->all(), [
            'title' => 'required|string|max:255',
            'cv_data' => 'required|array',
            'template_type' => 'required|string|in:modern,classic,creative',
        ], [
            'template_type.required' => 'Template type is required',
            'template_type.in' => 'Template type must be one of: modern, classic, creative',
        ]);

        if ($validator->fails()) {
            Log::warning('CV Creation Validation Failed', [
                'errors' => $validator->errors(),
                'request_data' => $request->all()
            ]);
            return response()->json([
                'success' => false,
                'message' => 'Validation errors',
                'errors' => $validator->errors()
            ], 422);
        }

        // Validate template file exists
        if (!$this->validateTemplate($request->template_type)) {
            Log::error('Invalid template type or missing template file', [
                'template_type' => $request->template_type
            ]);
            return response()->json([
                'success' => false,
                'message' => 'Invalid template type or template file not found'
            ], 422);
        }

        // Validate and normalize CV data
        $normalizedCvData = $this->validateAndNormalizeCVData($request->cv_data);

        $cv = $request->user()->cvs()->create([
            'title' => $request->title,
            'cv_data' => $normalizedCvData,
            'template_type' => $request->template_type,
        ]);

        Log::info('CV Created Successfully', [
            'cv_id' => $cv->id,
            'template_type' => $cv->template_type,
            'title' => $cv->title
        ]);

        return response()->json([
            'success' => true,
            'message' => 'CV created successfully',
            'data' => [
                'cv' => $cv
            ]
        ], 201);
    }

    /**
     * Display the specified CV.
     */
    public function show(Request $request, string $id): JsonResponse
    {
        $cv = $request->user()->cvs()->findOrFail($id);

        return response()->json([
            'success' => true,
            'data' => [
                'cv' => $cv
            ]
        ]);
    }

    /**
     * Update the specified CV in storage.
     */
    public function update(Request $request, string $id): JsonResponse
    {
        $cv = $request->user()->cvs()->findOrFail($id);

        // Log incoming update request
        Log::info('CV Update Request', [
            'cv_id' => $id,
            'current_template' => $cv->template_type,
            'new_template' => $request->template_type,
            'title' => $request->title,
            'request_data' => $request->only(['title', 'template_type'])
        ]);

        $validator = Validator::make($request->all(), [
            'title' => 'sometimes|required|string|max:255',
            'cv_data' => 'sometimes|required|array',
            'template_type' => 'sometimes|required|string|in:modern,classic,creative',
        ], [
            'template_type.required' => 'Template type is required',
            'template_type.in' => 'Template type must be one of: modern, classic, creative',
        ]);

        if ($validator->fails()) {
            Log::warning('CV Update Validation Failed', [
                'cv_id' => $id,
                'errors' => $validator->errors(),
                'request_data' => $request->all()
            ]);
            return response()->json([
                'success' => false,
                'message' => 'Validation errors',
                'errors' => $validator->errors()
            ], 422);
        }

        // Validate template file exists if template_type is being updated
        if ($request->has('template_type') && !$this->validateTemplate($request->template_type)) {
            Log::error('Invalid template type or missing template file in update', [
                'cv_id' => $id,
                'template_type' => $request->template_type
            ]);
            return response()->json([
                'success' => false,
                'message' => 'Invalid template type or template file not found'
            ], 422);
        }

        // Prepare update data
        $updateData = $request->only(['title', 'template_type']);

        // Validate and normalize CV data if provided
        if ($request->has('cv_data')) {
            $updateData['cv_data'] = $this->validateAndNormalizeCVData($request->cv_data);
        }

        $cv->update($updateData);

        Log::info('CV Updated Successfully', [
            'cv_id' => $cv->id,
            'template_type' => $cv->template_type,
            'title' => $cv->title
        ]);

        return response()->json([
            'success' => true,
            'message' => 'CV updated successfully',
            'data' => [
                'cv' => $cv->fresh()
            ]
        ]);
    }

    /**
     * Remove the specified CV from storage.
     */
    public function destroy(Request $request, string $id): JsonResponse
    {
        $cv = $request->user()->cvs()->findOrFail($id);
        $cv->delete();

        return response()->json([
            'success' => true,
            'message' => 'CV deleted successfully'
        ]);
    }

    /**
     * Generate and download CV as PDF with enhanced template handling.
     */
    public function downloadPdf(Request $request, string $id)
    {
        try {
            $cv = $request->user()->cvs()->findOrFail($id);

            // Enhanced template validation
            $templateType = $this->validateAndFixTemplate($cv->template_type, $id);
            if ($templateType !== $cv->template_type) {
                $cv->template_type = $templateType;
                $cv->save(); // Update the CV with corrected template
            }

            // Validate and normalize CV data
            $cv->cv_data = $this->validateAndNormalizeCVData($cv->cv_data);

            // Log PDF generation start
            Log::info("Starting PDF generation", [
                'cv_id' => $id,
                'template_type' => $cv->template_type,
                'cv_title' => $cv->title,
                'data_structure' => array_keys($cv->cv_data),
                'user_id' => $request->user()->id
            ]);

            // Generate PDF with enhanced error handling
            $pdf = $this->generatePDF($cv);

            // Log successful generation
            Log::info("PDF generated successfully", [
                'cv_id' => $id,
                'template_type' => $cv->template_type,
                'file_name' => $cv->title . '.pdf'
            ]);

            return $pdf->download($cv->title . '.pdf');

        } catch (\Exception $e) {
            Log::error("PDF generation failed", [
                'cv_id' => $id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'template_type' => $cv->template_type ?? 'unknown',
                'cv_title' => $cv->title ?? 'unknown',
                'user_id' => $request->user()->id ?? 'unknown'
            ]);

            return response()->json([
                'success' => false,
                'message' => 'PDF generation failed: ' . $e->getMessage(),
                'error_code' => 'PDF_GENERATION_FAILED'
            ], 500);
        }
    }

    /**
     * Validate and fix template type with fallback logic.
     */
    private function validateAndFixTemplate(string $templateType, string $cvId): string
    {
        // Check if template type is valid
        if (!in_array($templateType, ['modern', 'classic', 'creative'])) {
            Log::warning("Invalid template type, using fallback", [
                'cv_id' => $cvId,
                'invalid_template' => $templateType,
                'fallback_template' => 'modern'
            ]);
            return 'modern';
        }

        // Check if template file exists
        $templatePath = resource_path("views/cv/templates/{$templateType}.blade.php");
        if (!file_exists($templatePath) || !is_readable($templatePath)) {
            Log::error("Template file not found or not readable", [
                'cv_id' => $cvId,
                'template_type' => $templateType,
                'template_path' => $templatePath,
                'fallback_template' => 'modern'
            ]);
            return 'modern';
        }

        return $templateType;
    }

    /**
     * Generate PDF with optimized settings.
     */
    private function generatePDF(CV $cv)
    {
        // Ensure required directories exist
        $this->ensurePDFDirectories();

        // Load the template view
        $templateView = "cv.templates.{$cv->template_type}";

        Log::info("Loading template view", [
            'template_view' => $templateView,
            'template_type' => $cv->template_type,
            'cv_id' => $cv->id
        ]);

        // Configure PDF with optimized settings
        $pdf = Pdf::loadView($templateView, compact('cv'))
            ->setPaper('A4', 'portrait')
            ->setOptions([
                'isHtml5ParserEnabled' => true,
                'isPhpEnabled' => true,
                'defaultFont' => 'Arial',
                'dpi' => 96,
                'defaultPaperSize' => 'A4',
                'chroot' => public_path(),
                'logOutputFile' => storage_path('logs/dompdf.log'),
                'enable_font_subsetting' => false,
                'pdf_backend' => 'CPDF',
                'enable_javascript' => false,
                'enable_remote' => false,
                'enable_html5_parser' => true,
                'debugKeepTemp' => false,
                'debugPng' => false,
                'debugCss' => false,
                'fontDir' => storage_path('fonts/'),
                'fontCache' => storage_path('fonts/'),
                'tempDir' => storage_path('app/dompdf/'),
                'rootDir' => base_path(),
                'isRemoteEnabled' => false,
                'isJavascriptEnabled' => false
            ]);

        return $pdf;
    }

    /**
     * Ensure required directories exist for PDF generation.
     */
    private function ensurePDFDirectories(): void
    {
        $directories = [
            storage_path('fonts/'),
            storage_path('app/dompdf/'),
            storage_path('logs/')
        ];

        foreach ($directories as $directory) {
            if (!is_dir($directory)) {
                mkdir($directory, 0755, true);
                Log::info("Created directory: {$directory}");
            }
        }
    }

    /**
     * Preview CV template without downloading (for debugging).
     */
    public function previewTemplate(Request $request, string $id)
    {
        try {
            $cv = $request->user()->cvs()->findOrFail($id);

            // Validate and fix template
            $templateType = $this->validateAndFixTemplate($cv->template_type, $id);
            $cv->template_type = $templateType;

            // Normalize CV data
            $cv->cv_data = $this->validateAndNormalizeCVData($cv->cv_data);

            Log::info("Template preview requested", [
                'cv_id' => $id,
                'template_type' => $cv->template_type,
                'user_id' => $request->user()->id
            ]);

            // Return the rendered template as HTML
            return view("cv.templates.{$cv->template_type}", compact('cv'));

        } catch (\Exception $e) {
            Log::error("Template preview failed", [
                'cv_id' => $id,
                'error' => $e->getMessage(),
                'user_id' => $request->user()->id ?? 'unknown'
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Template preview failed: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get available templates information.
     */
    public function getTemplates()
    {
        $templates = [
            'modern' => [
                'id' => 'modern',
                'name' => 'Modern',
                'description' => 'Clean and contemporary design with blue accents',
                'features' => ['Gradient header', 'Modern typography', 'Clean layout'],
                'available' => $this->validateTemplate('modern')
            ],
            'classic' => [
                'id' => 'classic',
                'name' => 'Classic',
                'description' => 'Traditional and timeless design',
                'features' => ['Professional styling', 'Serif fonts', 'Formal layout'],
                'available' => $this->validateTemplate('classic')
            ],
            'creative' => [
                'id' => 'creative',
                'name' => 'Creative',
                'description' => 'Eye-catching design with gradients and modern elements',
                'features' => ['Gradient design', 'Modern elements', 'Creative layout'],
                'available' => $this->validateTemplate('creative')
            ]
        ];

        Log::info("Templates information requested", [
            'available_templates' => array_keys(array_filter($templates, fn($t) => $t['available']))
        ]);

        return response()->json([
            'success' => true,
            'data' => [
                'templates' => array_values($templates),
                'default' => 'modern'
            ]
        ]);
    }

    /**
     * Test template rendering with sample data.
     */
    public function testTemplate(Request $request, string $templateType)
    {
        try {
            // Validate template type
            if (!$this->validateTemplate($templateType)) {
                return response()->json([
                    'success' => false,
                    'message' => "Template '{$templateType}' not found or invalid"
                ], 404);
            }

            // Create sample CV data
            $sampleCV = new CV([
                'id' => 'sample',
                'title' => "Sample CV - {$templateType} Template",
                'template_type' => $templateType,
                'cv_data' => $this->getSampleCVData()
            ]);

            Log::info("Template test requested", [
                'template_type' => $templateType,
                'user_id' => $request->user()->id ?? 'anonymous'
            ]);

            // Return rendered template or PDF based on request
            if ($request->get('format') === 'pdf') {
                $pdf = $this->generatePDF($sampleCV);
                return $pdf->download("sample-{$templateType}.pdf");
            } else {
                return view("cv.templates.{$templateType}", ['cv' => $sampleCV]);
            }

        } catch (\Exception $e) {
            Log::error("Template test failed", [
                'template_type' => $templateType,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Template test failed: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get sample CV data for testing templates.
     */
    private function getSampleCVData(): array
    {
        return [
            'personal' => [
                'name' => 'John Doe',
                'title' => 'Senior Software Engineer',
                'summary' => 'Experienced software engineer with 8+ years in full-stack development. Expert in React, Laravel, and cloud technologies.'
            ],
            'contact' => [
                'email' => '<EMAIL>',
                'phone' => '+****************',
                'location' => 'San Francisco, CA',
                'linkedin' => 'linkedin.com/in/johndoe'
            ],
            'skills' => [
                'Frontend' => ['React', 'TypeScript', 'Vue.js', 'HTML5', 'CSS3'],
                'Backend' => ['Laravel', 'Node.js', 'Python', 'PHP'],
                'Database' => ['MySQL', 'PostgreSQL', 'MongoDB'],
                'DevOps' => ['Docker', 'AWS', 'CI/CD', 'Jenkins']
            ],
            'experience' => [
                [
                    'position' => 'Senior Software Engineer',
                    'company' => 'Tech Corp',
                    'start_date' => '2020-01',
                    'end_date' => 'Present',
                    'description' => 'Leading development of scalable web applications. Mentoring junior developers and implementing best practices.'
                ],
                [
                    'position' => 'Full-Stack Developer',
                    'company' => 'StartupXYZ',
                    'start_date' => '2018-06',
                    'end_date' => '2019-12',
                    'description' => 'Developed responsive web applications using modern frameworks. Collaborated with cross-functional teams.'
                ]
            ],
            'education' => [
                [
                    'degree' => 'Bachelor of Computer Science',
                    'institution' => 'University of Technology',
                    'start_date' => '2014-09',
                    'end_date' => '2018-05',
                    'description' => 'Graduated with honors. Specialized in software engineering and web development.'
                ]
            ],
            'projects' => [
                [
                    'name' => 'E-commerce Platform',
                    'date' => '2023',
                    'description' => 'Built a scalable e-commerce platform with real-time inventory management.',
                    'technologies' => 'React, Laravel, MySQL, Redis'
                ]
            ],
            'languages' => [
                ['name' => 'English', 'level' => 'Native'],
                ['name' => 'Spanish', 'level' => 'Intermediate']
            ],
            'hobbies' => ['Programming', 'Reading', 'Hiking', 'Photography']
        ];
    }
}
