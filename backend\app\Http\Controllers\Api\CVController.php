<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\CV;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;
use Barryvdh\DomPDF\Facade\Pdf;

class CVController extends Controller
{
    /**
     * Display a listing of the user's CVs.
     */
    public function index(Request $request): JsonResponse
    {
        $cvs = $request->user()->cvs()->orderBy('created_at', 'desc')->get();

        return response()->json([
            'success' => true,
            'data' => [
                'cvs' => $cvs
            ]
        ]);
    }

    /**
     * Store a newly created CV in storage.
     */
    public function store(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'title' => 'required|string|max:255',
            'cv_data' => 'required|array',
            'template_type' => 'required|string|in:modern,classic,creative',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation errors',
                'errors' => $validator->errors()
            ], 422);
        }

        $cv = $request->user()->cvs()->create([
            'title' => $request->title,
            'cv_data' => $request->cv_data,
            'template_type' => $request->template_type,
        ]);

        return response()->json([
            'success' => true,
            'message' => 'CV created successfully',
            'data' => [
                'cv' => $cv
            ]
        ], 201);
    }

    /**
     * Display the specified CV.
     */
    public function show(Request $request, string $id): JsonResponse
    {
        $cv = $request->user()->cvs()->findOrFail($id);

        return response()->json([
            'success' => true,
            'data' => [
                'cv' => $cv
            ]
        ]);
    }

    /**
     * Update the specified CV in storage.
     */
    public function update(Request $request, string $id): JsonResponse
    {
        $cv = $request->user()->cvs()->findOrFail($id);

        $validator = Validator::make($request->all(), [
            'title' => 'sometimes|required|string|max:255',
            'cv_data' => 'sometimes|required|array',
            'template_type' => 'sometimes|required|string|in:modern,classic,creative',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation errors',
                'errors' => $validator->errors()
            ], 422);
        }

        $cv->update($request->only(['title', 'cv_data', 'template_type']));

        return response()->json([
            'success' => true,
            'message' => 'CV updated successfully',
            'data' => [
                'cv' => $cv->fresh()
            ]
        ]);
    }

    /**
     * Remove the specified CV from storage.
     */
    public function destroy(Request $request, string $id): JsonResponse
    {
        $cv = $request->user()->cvs()->findOrFail($id);
        $cv->delete();

        return response()->json([
            'success' => true,
            'message' => 'CV deleted successfully'
        ]);
    }

    /**
     * Generate and download CV as PDF.
     */
    public function downloadPdf(Request $request, string $id)
    {
        try {
            $cv = $request->user()->cvs()->findOrFail($id);

            // Log template type for debugging
            Log::info("Generating PDF for CV ID: $id, Template: " . $cv->template_type);

            $pdf = Pdf::loadView('cv.templates.' . $cv->template_type, compact('cv'));

            return $pdf->download($cv->title . '.pdf');
        } catch (\Exception $e) {
            Log::error("PDF generation failed for CV ID: $id", [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'PDF generation failed: ' . $e->getMessage()
            ], 500);
        }
    }
}
