<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\CV;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;
use Barryvdh\DomPDF\Facade\Pdf;

class CVController extends Controller
{
    /**
     * Validate and normalize CV data structure
     */
    private function validateAndNormalizeCVData(array $cvData): array
    {
        $requiredKeys = ['personal', 'contact', 'skills', 'experience', 'education', 'projects', 'languages', 'hobbies'];

        foreach ($requiredKeys as $key) {
            if (!isset($cvData[$key])) {
                $cvData[$key] = [];
            }
        }

        // Ensure personal data has required fields
        if (!isset($cvData['personal']['name'])) {
            $cvData['personal']['name'] = '';
        }
        if (!isset($cvData['personal']['title'])) {
            $cvData['personal']['title'] = '';
        }
        if (!isset($cvData['personal']['summary'])) {
            $cvData['personal']['summary'] = '';
        }

        // Ensure contact data is properly structured
        if (!is_array($cvData['contact'])) {
            $cvData['contact'] = [];
        }

        // Ensure arrays are properly structured
        foreach (['experience', 'education', 'projects', 'languages', 'hobbies'] as $arrayKey) {
            if (!is_array($cvData[$arrayKey])) {
                $cvData[$arrayKey] = [];
            }
        }

        // Ensure skills is properly structured (can be array or object)
        if (!isset($cvData['skills']) || (!is_array($cvData['skills']) && !is_object($cvData['skills']))) {
            $cvData['skills'] = [];
        }

        return $cvData;
    }

    /**
     * Validate template type and file existence
     */
    private function validateTemplate(string $templateType): bool
    {
        // Check if template type is valid
        if (!in_array($templateType, ['modern', 'classic', 'creative'])) {
            return false;
        }

        // Check if template file exists
        $templatePath = resource_path("views/cv/templates/{$templateType}.blade.php");
        return file_exists($templatePath) && is_readable($templatePath);
    }

    /**
     * Display a listing of the user's CVs.
     */
    public function index(Request $request): JsonResponse
    {
        $cvs = $request->user()->cvs()->orderBy('created_at', 'desc')->get();

        return response()->json([
            'success' => true,
            'data' => [
                'cvs' => $cvs
            ]
        ]);
    }

    /**
     * Store a newly created CV in storage.
     */
    public function store(Request $request): JsonResponse
    {
        // Log incoming request for debugging
        Log::info('CV Creation Request', [
            'title' => $request->title,
            'template_type' => $request->template_type,
            'has_cv_data' => !empty($request->cv_data),
            'all_data' => $request->all()
        ]);

        $validator = Validator::make($request->all(), [
            'title' => 'required|string|max:255',
            'cv_data' => 'required|array',
            'template_type' => 'required|string|in:modern,classic,creative',
        ], [
            'template_type.required' => 'Template type is required',
            'template_type.in' => 'Template type must be one of: modern, classic, creative',
        ]);

        if ($validator->fails()) {
            Log::warning('CV Creation Validation Failed', [
                'errors' => $validator->errors(),
                'request_data' => $request->all()
            ]);
            return response()->json([
                'success' => false,
                'message' => 'Validation errors',
                'errors' => $validator->errors()
            ], 422);
        }

        // Validate template file exists
        if (!$this->validateTemplate($request->template_type)) {
            Log::error('Invalid template type or missing template file', [
                'template_type' => $request->template_type
            ]);
            return response()->json([
                'success' => false,
                'message' => 'Invalid template type or template file not found'
            ], 422);
        }

        // Validate and normalize CV data
        $normalizedCvData = $this->validateAndNormalizeCVData($request->cv_data);

        $cv = $request->user()->cvs()->create([
            'title' => $request->title,
            'cv_data' => $normalizedCvData,
            'template_type' => $request->template_type,
        ]);

        Log::info('CV Created Successfully', [
            'cv_id' => $cv->id,
            'template_type' => $cv->template_type,
            'title' => $cv->title
        ]);

        return response()->json([
            'success' => true,
            'message' => 'CV created successfully',
            'data' => [
                'cv' => $cv
            ]
        ], 201);
    }

    /**
     * Display the specified CV.
     */
    public function show(Request $request, string $id): JsonResponse
    {
        $cv = $request->user()->cvs()->findOrFail($id);

        return response()->json([
            'success' => true,
            'data' => [
                'cv' => $cv
            ]
        ]);
    }

    /**
     * Update the specified CV in storage.
     */
    public function update(Request $request, string $id): JsonResponse
    {
        $cv = $request->user()->cvs()->findOrFail($id);

        // Log incoming update request
        Log::info('CV Update Request', [
            'cv_id' => $id,
            'current_template' => $cv->template_type,
            'new_template' => $request->template_type,
            'title' => $request->title,
            'request_data' => $request->only(['title', 'template_type'])
        ]);

        $validator = Validator::make($request->all(), [
            'title' => 'sometimes|required|string|max:255',
            'cv_data' => 'sometimes|required|array',
            'template_type' => 'sometimes|required|string|in:modern,classic,creative',
        ], [
            'template_type.required' => 'Template type is required',
            'template_type.in' => 'Template type must be one of: modern, classic, creative',
        ]);

        if ($validator->fails()) {
            Log::warning('CV Update Validation Failed', [
                'cv_id' => $id,
                'errors' => $validator->errors(),
                'request_data' => $request->all()
            ]);
            return response()->json([
                'success' => false,
                'message' => 'Validation errors',
                'errors' => $validator->errors()
            ], 422);
        }

        // Validate template file exists if template_type is being updated
        if ($request->has('template_type') && !$this->validateTemplate($request->template_type)) {
            Log::error('Invalid template type or missing template file in update', [
                'cv_id' => $id,
                'template_type' => $request->template_type
            ]);
            return response()->json([
                'success' => false,
                'message' => 'Invalid template type or template file not found'
            ], 422);
        }

        // Prepare update data
        $updateData = $request->only(['title', 'template_type']);

        // Validate and normalize CV data if provided
        if ($request->has('cv_data')) {
            $updateData['cv_data'] = $this->validateAndNormalizeCVData($request->cv_data);
        }

        $cv->update($updateData);

        Log::info('CV Updated Successfully', [
            'cv_id' => $cv->id,
            'template_type' => $cv->template_type,
            'title' => $cv->title
        ]);

        return response()->json([
            'success' => true,
            'message' => 'CV updated successfully',
            'data' => [
                'cv' => $cv->fresh()
            ]
        ]);
    }

    /**
     * Remove the specified CV from storage.
     */
    public function destroy(Request $request, string $id): JsonResponse
    {
        $cv = $request->user()->cvs()->findOrFail($id);
        $cv->delete();

        return response()->json([
            'success' => true,
            'message' => 'CV deleted successfully'
        ]);
    }

    /**
     * Generate and download CV as PDF.
     */
    public function downloadPdf(Request $request, string $id)
    {
        try {
            $cv = $request->user()->cvs()->findOrFail($id);

            // Validate template type
            if (!in_array($cv->template_type, ['modern', 'classic', 'creative'])) {
                Log::warning("Invalid template type for CV ID: $id", [
                    'template_type' => $cv->template_type
                ]);
                $cv->template_type = 'modern'; // Fallback to modern template
            }

            // Validate CV data structure
            if (empty($cv->cv_data) || !is_array($cv->cv_data)) {
                Log::warning("Invalid CV data structure for CV ID: $id", [
                    'cv_data_type' => gettype($cv->cv_data),
                    'cv_data_empty' => empty($cv->cv_data)
                ]);
                throw new \Exception('Invalid CV data structure');
            }

            // Normalize CV data structure
            $cv->cv_data = $this->validateAndNormalizeCVData($cv->cv_data);

            // Log template type for debugging
            Log::info("Generating PDF for CV ID: $id, Template: " . $cv->template_type, [
                'cv_title' => $cv->title,
                'data_keys' => array_keys($cv->cv_data)
            ]);

            // Check if template file exists
            $templatePath = resource_path("views/cv/templates/{$cv->template_type}.blade.php");
            if (!file_exists($templatePath)) {
                Log::error("Template file not found for CV ID: $id", [
                    'template_type' => $cv->template_type,
                    'template_path' => $templatePath
                ]);
                throw new \Exception("Template file not found: {$cv->template_type}");
            }

            // Configure PDF options for single page rendering
            $pdf = Pdf::loadView('cv.templates.' . $cv->template_type, compact('cv'))
                ->setPaper('A4', 'portrait')
                ->setOptions([
                    'isHtml5ParserEnabled' => true,
                    'isPhpEnabled' => true,
                    'defaultFont' => 'Arial',
                    'dpi' => 150,
                    'defaultPaperSize' => 'A4',
                    'chroot' => public_path(),
                    'logOutputFile' => storage_path('logs/dompdf.log'),
                    'enable_font_subsetting' => false,
                    'pdf_backend' => 'CPDF',
                    'enable_javascript' => false,
                    'enable_remote' => false,
                    'enable_html5_parser' => true
                ]);

            Log::info("PDF generated successfully for CV ID: $id", [
                'template_type' => $cv->template_type,
                'cv_title' => $cv->title,
                'pdf_size' => 'Generated'
            ]);

            return $pdf->download($cv->title . '.pdf');
        } catch (\Exception $e) {
            Log::error("PDF generation failed for CV ID: $id", [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'cv_template' => $cv->template_type ?? 'unknown',
                'cv_title' => $cv->title ?? 'unknown'
            ]);

            return response()->json([
                'success' => false,
                'message' => 'PDF generation failed: ' . $e->getMessage()
            ], 500);
        }
    }
}
