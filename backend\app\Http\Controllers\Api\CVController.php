<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\CV;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;
use Barryvdh\DomPDF\Facade\Pdf;

class CVController extends Controller
{
    /**
     * Display a listing of the user's CVs.
     */
    public function index(Request $request): JsonResponse
    {
        $cvs = $request->user()->cvs()->orderBy('created_at', 'desc')->get();

        return response()->json([
            'success' => true,
            'data' => [
                'cvs' => $cvs
            ]
        ]);
    }

    /**
     * Store a newly created CV in storage.
     */
    public function store(Request $request): JsonResponse
    {
        // Log incoming request for debugging
        Log::info('CV Creation Request', [
            'title' => $request->title,
            'template_type' => $request->template_type,
            'has_cv_data' => !empty($request->cv_data),
            'all_data' => $request->all()
        ]);

        $validator = Validator::make($request->all(), [
            'title' => 'required|string|max:255',
            'cv_data' => 'required|array',
            'template_type' => 'required|string|in:modern,classic,creative',
        ]);

        if ($validator->fails()) {
            Log::warning('CV Creation Validation Failed', [
                'errors' => $validator->errors(),
                'request_data' => $request->all()
            ]);
            return response()->json([
                'success' => false,
                'message' => 'Validation errors',
                'errors' => $validator->errors()
            ], 422);
        }

        $cv = $request->user()->cvs()->create([
            'title' => $request->title,
            'cv_data' => $request->cv_data,
            'template_type' => $request->template_type,
        ]);

        Log::info('CV Created Successfully', [
            'cv_id' => $cv->id,
            'template_type' => $cv->template_type,
            'title' => $cv->title
        ]);

        return response()->json([
            'success' => true,
            'message' => 'CV created successfully',
            'data' => [
                'cv' => $cv
            ]
        ], 201);
    }

    /**
     * Display the specified CV.
     */
    public function show(Request $request, string $id): JsonResponse
    {
        $cv = $request->user()->cvs()->findOrFail($id);

        return response()->json([
            'success' => true,
            'data' => [
                'cv' => $cv
            ]
        ]);
    }

    /**
     * Update the specified CV in storage.
     */
    public function update(Request $request, string $id): JsonResponse
    {
        $cv = $request->user()->cvs()->findOrFail($id);

        // Log incoming update request
        Log::info('CV Update Request', [
            'cv_id' => $id,
            'current_template' => $cv->template_type,
            'new_template' => $request->template_type,
            'title' => $request->title,
            'request_data' => $request->only(['title', 'template_type'])
        ]);

        $validator = Validator::make($request->all(), [
            'title' => 'sometimes|required|string|max:255',
            'cv_data' => 'sometimes|required|array',
            'template_type' => 'sometimes|required|string|in:modern,classic,creative',
        ]);

        if ($validator->fails()) {
            Log::warning('CV Update Validation Failed', [
                'cv_id' => $id,
                'errors' => $validator->errors(),
                'request_data' => $request->all()
            ]);
            return response()->json([
                'success' => false,
                'message' => 'Validation errors',
                'errors' => $validator->errors()
            ], 422);
        }

        $cv->update($request->only(['title', 'cv_data', 'template_type']));

        Log::info('CV Updated Successfully', [
            'cv_id' => $cv->id,
            'template_type' => $cv->template_type,
            'title' => $cv->title
        ]);

        return response()->json([
            'success' => true,
            'message' => 'CV updated successfully',
            'data' => [
                'cv' => $cv->fresh()
            ]
        ]);
    }

    /**
     * Remove the specified CV from storage.
     */
    public function destroy(Request $request, string $id): JsonResponse
    {
        $cv = $request->user()->cvs()->findOrFail($id);
        $cv->delete();

        return response()->json([
            'success' => true,
            'message' => 'CV deleted successfully'
        ]);
    }

    /**
     * Generate and download CV as PDF.
     */
    public function downloadPdf(Request $request, string $id)
    {
        try {
            $cv = $request->user()->cvs()->findOrFail($id);

            // Log template type for debugging
            Log::info("Generating PDF for CV ID: $id, Template: " . $cv->template_type);

            $pdf = Pdf::loadView('cv.templates.' . $cv->template_type, compact('cv'));

            return $pdf->download($cv->title . '.pdf');
        } catch (\Exception $e) {
            Log::error("PDF generation failed for CV ID: $id", [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'PDF generation failed: ' . $e->getMessage()
            ], 500);
        }
    }
}
