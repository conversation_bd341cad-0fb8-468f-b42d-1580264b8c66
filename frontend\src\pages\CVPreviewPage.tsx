import React, { useState, useEffect } from 'react';
import { use<PERSON>ara<PERSON>, Link } from 'react-router-dom';
import { CV } from '../types';
import { apiService, handleApiError } from '../services/api';
import { Download, Edit, ArrowLeft, Eye } from 'lucide-react';
import CVPreview from '../components/CVPreview/CVPreview';
import LoadingSpinner from '../components/LoadingSpinner';

const CVPreviewPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const [cv, setCv] = useState<CV | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isDownloading, setIsDownloading] = useState(false);
  const [error, setError] = useState('');

  useEffect(() => {
    if (id) {
      fetchCV();
    }
  }, [id]);

  const fetchCV = async () => {
    try {
      setIsLoading(true);
      const response = await apiService.getCV(Number(id));
      if (response.success && response.data.cv) {
        setCv(response.data.cv);
      }
    } catch (err) {
      setError(handleApiError(err));
    } finally {
      setIsLoading(false);
    }
  };

  const handleDownloadPDF = async () => {
    if (!cv) return;

    try {
      setIsDownloading(true);
      const blob = await apiService.downloadCVPDF(cv.id);
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `${cv.title}.pdf`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
    } catch (err) {
      setError(handleApiError(err));
    } finally {
      setIsDownloading(false);
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="text-center">
          <div className="rounded-md bg-red-50 dark:bg-red-900/50 p-4">
            <p className="text-sm text-red-800 dark:text-red-200">{error}</p>
          </div>
          <Link to="/dashboard" className="mt-4 btn-primary inline-flex items-center">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Dashboard
          </Link>
        </div>
      </div>
    );
  }

  if (!cv) {
    return (
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="text-center">
          <Eye className="mx-auto h-12 w-12 text-gray-400" />
          <h1 className="mt-4 text-3xl font-bold text-gray-900 dark:text-white">
            CV Not Found
          </h1>
          <p className="mt-2 text-gray-600 dark:text-gray-400">
            The CV you're looking for doesn't exist or you don't have permission to view it.
          </p>
          <Link to="/dashboard" className="mt-4 btn-primary inline-flex items-center">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Dashboard
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* Header */}
      <div className="flex justify-between items-center mb-8">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
            {cv.title}
          </h1>
          <p className="text-gray-600 dark:text-gray-400 capitalize">
            {cv.template_type} template • Last updated {new Date(cv.updated_at).toLocaleDateString()}
          </p>
        </div>
        <div className="flex space-x-3">
          <Link
            to="/dashboard"
            className="btn-outline"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Dashboard
          </Link>
          <Link
            to={`/cv/${cv.id}/edit`}
            className="btn-outline"
          >
            <Edit className="h-4 w-4 mr-2" />
            Edit
          </Link>
          <button
            onClick={handleDownloadPDF}
            disabled={isDownloading}
            className="btn-primary disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isDownloading ? (
              <>
                <LoadingSpinner size="sm" className="mr-2" />
                Downloading...
              </>
            ) : (
              <>
                <Download className="h-4 w-4 mr-2" />
                Download PDF
              </>
            )}
          </button>
        </div>
      </div>

      {/* CV Preview */}
      <div className="bg-white shadow-lg rounded-lg overflow-hidden">
        <CVPreview data={cv.cv_data} template={cv.template_type} />
      </div>
    </div>
  );
};

export default CVPreviewPage;
