import React, { useState } from 'react';
import { FormStepProps, Skills } from '../../../types';
import { Settings, Plus, X } from 'lucide-react';

const SkillsStep: React.FC<FormStepProps> = ({ data, updateData, onNext, onPrevious }) => {
  const [skills, setSkills] = useState<Skills>(data.skills || {});
  const [newCategory, setNewCategory] = useState('');
  const [newSkill, setNewSkill] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('');

  const predefinedCategories = [
    'Technical Skills',
    'Programming Languages',
    'Frameworks & Libraries',
    'Tools & Software',
    'Databases',
    'Soft Skills',
    'Languages',
    'Certifications'
  ];

  const addCategory = () => {
    if (newCategory.trim() && !skills[newCategory]) {
      setSkills(prev => ({
        ...prev,
        [newCategory]: []
      }));
      setNewCategory('');
    }
  };

  const removeCategory = (category: string) => {
    const updated = { ...skills };
    delete updated[category];
    setSkills(updated);
  };

  const addSkill = () => {
    if (newSkill.trim() && selectedCategory && skills[selectedCategory]) {
      setSkills(prev => ({
        ...prev,
        [selectedCategory]: [...prev[selectedCategory], newSkill.trim()]
      }));
      setNewSkill('');
    }
  };

  const removeSkill = (category: string, skillIndex: number) => {
    setSkills(prev => ({
      ...prev,
      [category]: prev[category].filter((_, index) => index !== skillIndex)
    }));
  };

  const handleNext = () => {
    updateData('skills', skills);
    onNext();
  };

  const handleKeyPress = (e: React.KeyboardEvent, action: () => void) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      action();
    }
  };

  return (
    <div>
      <div className="flex items-center mb-6">
        <Settings className="h-6 w-6 text-primary-600 mr-3" />
        <div>
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
            Skills & Competencies
          </h2>
          <p className="text-gray-600 dark:text-gray-400">
            Organize your skills into categories to showcase your expertise
          </p>
        </div>
      </div>

      {/* Add New Category */}
      <div className="mb-6 bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-3">
          Add Skill Category
        </h3>
        <div className="flex flex-wrap gap-2 mb-3">
          {predefinedCategories.map(category => (
            <button
              key={category}
              onClick={() => {
                if (!skills[category]) {
                  setSkills(prev => ({ ...prev, [category]: [] }));
                }
              }}
              disabled={!!skills[category]}
              className="px-3 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-100 dark:hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {category}
            </button>
          ))}
        </div>
        <div className="flex gap-2">
          <input
            type="text"
            value={newCategory}
            onChange={(e) => setNewCategory(e.target.value)}
            onKeyPress={(e) => handleKeyPress(e, addCategory)}
            placeholder="Custom category name"
            className="flex-1 input"
          />
          <button
            onClick={addCategory}
            disabled={!newCategory.trim() || !!skills[newCategory]}
            className="btn-primary disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <Plus className="h-4 w-4" />
          </button>
        </div>
      </div>

      {/* Skills Categories */}
      {Object.keys(skills).length > 0 && (
        <div className="mb-6 space-y-4">
          {Object.entries(skills).map(([category, skillList]) => (
            <div key={category} className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-4">
              <div className="flex justify-between items-center mb-3">
                <h4 className="text-lg font-medium text-gray-900 dark:text-white">
                  {category}
                </h4>
                <button
                  onClick={() => removeCategory(category)}
                  className="p-1 text-gray-500 hover:text-red-600"
                >
                  <X className="h-4 w-4" />
                </button>
              </div>

              {/* Skills in this category */}
              <div className="flex flex-wrap gap-2 mb-3">
                {skillList.map((skill, index) => (
                  <span
                    key={index}
                    className="inline-flex items-center px-3 py-1 rounded-full text-sm bg-primary-100 text-primary-800 dark:bg-primary-900 dark:text-primary-200"
                  >
                    {skill}
                    <button
                      onClick={() => removeSkill(category, index)}
                      className="ml-2 text-primary-600 hover:text-primary-800"
                    >
                      <X className="h-3 w-3" />
                    </button>
                  </span>
                ))}
              </div>

              {/* Add skill to this category */}
              <div className="flex gap-2">
                <input
                  type="text"
                  value={selectedCategory === category ? newSkill : ''}
                  onChange={(e) => {
                    setNewSkill(e.target.value);
                    setSelectedCategory(category);
                  }}
                  onKeyPress={(e) => handleKeyPress(e, addSkill)}
                  placeholder={`Add skill to ${category}`}
                  className="flex-1 input"
                />
                <button
                  onClick={() => {
                    setSelectedCategory(category);
                    addSkill();
                  }}
                  disabled={!newSkill.trim() || selectedCategory !== category}
                  className="btn-outline disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <Plus className="h-4 w-4" />
                </button>
              </div>
            </div>
          ))}
        </div>
      )}

      {Object.keys(skills).length === 0 && (
        <div className="text-center py-8 text-gray-500 dark:text-gray-400">
          <Settings className="mx-auto h-12 w-12 mb-4 opacity-50" />
          <p>No skill categories added yet. Start by adding a category above.</p>
        </div>
      )}

      {/* Navigation */}
      <div className="flex justify-between">
        <button type="button" onClick={onPrevious} className="btn-outline">
          Previous
        </button>
        <button type="button" onClick={handleNext} className="btn-primary">
          Next: Projects
        </button>
      </div>
    </div>
  );
};

export default SkillsStep;
