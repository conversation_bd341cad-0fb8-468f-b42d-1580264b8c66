import React, { useRef, useState } from 'react';
import { CVData } from '../../types';
import ModernTemplate from '../CVPreview/templates/ModernTemplate';
import ClassicTemplate from '../CVPreview/templates/ClassicTemplate';
import CreativeTemplate from '../CVPreview/templates/CreativeTemplate';
import { Download, Printer, FileText } from 'lucide-react';

interface SimplePDFExporterProps {
  data: CVData;
  templateType: 'modern' | 'classic' | 'creative';
  fileName?: string;
  className?: string;
}

const SimplePDFExporter: React.FC<SimplePDFExporterProps> = ({
  data,
  templateType,
  fileName = 'my-cv',
  className = ''
}) => {
  const [isExporting, setIsExporting] = useState(false);
  const hiddenRef = useRef<HTMLDivElement>(null);

  // Template component mapping
  const getTemplateComponent = () => {
    const props = { data };
    
    switch (templateType) {
      case 'modern':
        return <ModernTemplate {...props} />;
      case 'classic':
        return <ClassicTemplate {...props} />;
      case 'creative':
        return <CreativeTemplate {...props} />;
      default:
        return <ModernTemplate {...props} />;
    }
  };

  // Browser print method (works immediately)
  const printCV = async () => {
    if (!hiddenRef.current) return;

    try {
      setIsExporting(true);
      console.log('🎯 Starting print with template:', templateType);

      // Wait for DOM update
      await new Promise(resolve => setTimeout(resolve, 300));

      const element = hiddenRef.current;
      const content = element.innerHTML;

      // Create optimized print styles
      const printStyles = `
        <style>
          @page {
            size: A4;
            margin: 0.4in;
          }
          
          @media print {
            body {
              font-family: Arial, sans-serif !important;
              font-size: 11px !important;
              line-height: 1.3 !important;
              color: #000 !important;
              background: white !important;
              margin: 0 !important;
              padding: 0 !important;
            }
            
            * {
              page-break-inside: avoid !important;
              break-inside: avoid !important;
            }
            
            .print-container {
              width: 100% !important;
              max-width: none !important;
              margin: 0 !important;
              padding: 15px !important;
              box-sizing: border-box !important;
            }
            
            /* Modern template styles */
            .header {
              background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%) !important;
              color: white !important;
              padding: 16px !important;
              margin-bottom: 16px !important;
              text-align: center !important;
              border-radius: 0 !important;
            }
            
            .header h1 {
              font-size: 20px !important;
              margin-bottom: 6px !important;
              color: white !important;
              font-weight: bold !important;
            }
            
            .header .title {
              font-size: 14px !important;
              margin-bottom: 10px !important;
              color: white !important;
              opacity: 0.9 !important;
            }
            
            .contact-info {
              display: flex !important;
              justify-content: center !important;
              gap: 12px !important;
              flex-wrap: wrap !important;
              font-size: 10px !important;
              color: white !important;
            }
            
            .contact-info span {
              color: white !important;
            }
            
            .section {
              margin-bottom: 12px !important;
            }
            
            .section h2 {
              font-size: 14px !important;
              color: #2563eb !important;
              border-bottom: 1px solid #2563eb !important;
              padding-bottom: 3px !important;
              margin-bottom: 8px !important;
              font-weight: bold !important;
            }
            
            .skills-grid {
              display: grid !important;
              grid-template-columns: 1fr 1fr !important;
              gap: 8px !important;
            }
            
            .skill-category {
              background: #f8f9fa !important;
              padding: 6px !important;
              border-radius: 3px !important;
              margin-bottom: 4px !important;
            }
            
            .skill-category h3 {
              font-size: 11px !important;
              margin-bottom: 3px !important;
              color: #2563eb !important;
              font-weight: bold !important;
            }
            
            .skill-list {
              font-size: 9px !important;
              line-height: 1.2 !important;
            }
            
            .experience-item, .education-item, .project-item {
              margin-bottom: 8px !important;
            }
            
            .experience-item h3, .education-item h3, .project-item h3 {
              font-size: 12px !important;
              margin-bottom: 2px !important;
              font-weight: bold !important;
              color: #000 !important;
            }
            
            .company, .institution {
              font-size: 10px !important;
              color: #666 !important;
              margin-bottom: 3px !important;
              font-style: italic !important;
            }
            
            .item-date {
              font-size: 9px !important;
              color: #888 !important;
              margin-bottom: 3px !important;
            }
            
            .summary, .description {
              font-size: 10px !important;
              line-height: 1.2 !important;
              margin-bottom: 4px !important;
              color: #333 !important;
            }
            
            .languages-list, .hobbies-list {
              font-size: 10px !important;
              line-height: 1.3 !important;
            }
            
            p, li, span, div {
              font-size: 10px !important;
              line-height: 1.2 !important;
              margin-bottom: 2px !important;
            }
            
            ul {
              margin: 0 !important;
              padding-left: 12px !important;
            }
            
            li {
              margin-bottom: 1px !important;
            }
            
            /* Hide non-essential elements */
            .no-print {
              display: none !important;
            }
          }
        </style>
      `;

      // Create print window
      const printWindow = window.open('', '_blank', 'width=800,height=600');
      if (!printWindow) {
        alert('Please allow popups to print the CV');
        return;
      }

      const printHTML = `
        <!DOCTYPE html>
        <html>
        <head>
          <title>${fileName}-${templateType}</title>
          <meta charset="utf-8">
          ${printStyles}
        </head>
        <body>
          <div class="print-container">
            ${content}
          </div>
          <script>
            window.onload = function() {
              setTimeout(() => {
                window.print();
                setTimeout(() => window.close(), 500);
              }, 500);
            };
          </script>
        </body>
        </html>
      `;

      printWindow.document.write(printHTML);
      printWindow.document.close();

      console.log('✅ Print window opened');

    } catch (error) {
      console.error('❌ Print failed:', error);
      alert('Print failed. Please try again.');
    } finally {
      setIsExporting(false);
    }
  };

  // Screenshot method (basic)
  const takeScreenshot = async () => {
    if (!hiddenRef.current) return;

    try {
      setIsExporting(true);
      console.log('📸 Taking screenshot of template:', templateType);

      // This is a basic implementation - for better results, install html2canvas
      alert('For high-quality PDF export, please install: npm install html2canvas jspdf');
      
    } catch (error) {
      console.error('❌ Screenshot failed:', error);
    } finally {
      setIsExporting(false);
    }
  };

  return (
    <div className={className}>
      {/* Export Options */}
      <div className="space-y-4">
        <h3 className="text-lg font-medium text-gray-900 dark:text-white">
          Export Options
          <span className="ml-2 text-sm text-blue-600 dark:text-blue-400">
            (Template: {templateType})
          </span>
        </h3>
        
        <div className="flex flex-wrap gap-3">
          {/* Print to PDF (Works immediately) */}
          <button
            onClick={printCV}
            disabled={isExporting}
            className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 transition-colors"
          >
            <Printer className="h-4 w-4 mr-2" />
            Print to PDF
          </button>
          
          {/* Screenshot option */}
          <button
            onClick={takeScreenshot}
            disabled={isExporting}
            className="inline-flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 transition-colors"
          >
            <FileText className="h-4 w-4 mr-2" />
            Screenshot
          </button>
          
          {/* Package installation reminder */}
          <button
            onClick={() => {
              const message = `To enable advanced PDF export, run:\n\nnpm install html2pdf.js\n\nThen refresh the page.`;
              alert(message);
            }}
            className="inline-flex items-center px-4 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors"
          >
            <Download className="h-4 w-4 mr-2" />
            Install Advanced Export
          </button>
        </div>
      </div>

      {/* Template Preview */}
      <div className="mt-6 border border-gray-200 rounded-lg overflow-hidden">
        <div className="bg-gray-50 px-4 py-2 border-b">
          <span className="text-sm font-medium text-gray-700">
            Preview: {templateType.charAt(0).toUpperCase() + templateType.slice(1)} Template
          </span>
        </div>
        <div className="p-4 bg-white">
          <div className="transform scale-75 origin-top-left" style={{ width: '133.33%' }}>
            {getTemplateComponent()}
          </div>
        </div>
      </div>

      {/* Hidden Template for Export */}
      <div
        ref={hiddenRef}
        style={{
          position: 'fixed',
          left: '-9999px',
          top: '0',
          width: '794px',
          height: 'auto',
          backgroundColor: '#ffffff',
          visibility: 'hidden',
          opacity: '0',
          zIndex: -1,
          fontFamily: 'Arial, sans-serif'
        }}
      >
        <div style={{ 
          width: '794px',
          minHeight: '1123px',
          padding: '20px',
          boxSizing: 'border-box'
        }}>
          {getTemplateComponent()}
        </div>
      </div>

      {/* Instructions */}
      <div className="mt-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
        <h4 className="text-blue-800 font-medium mb-2">📋 How to Export:</h4>
        <ol className="list-decimal list-inside space-y-1 text-sm text-blue-700">
          <li><strong>Print to PDF:</strong> Opens browser print dialog - choose "Save as PDF"</li>
          <li><strong>Template Selection:</strong> Change template above, then export</li>
          <li><strong>Single Page:</strong> Content is optimized to fit on one A4 page</li>
          <li><strong>Advanced Export:</strong> Install html2pdf.js for more options</li>
        </ol>
      </div>
    </div>
  );
};

export default SimplePDFExporter;
