import React, { useState } from 'react';
import { apiService } from '../services/api';

interface TemplateDebuggerProps {
  cvId?: number;
}

const TemplateDebugger: React.FC<TemplateDebuggerProps> = ({ cvId }) => {
  const [debugInfo, setDebugInfo] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [selectedTemplate, setSelectedTemplate] = useState<'modern' | 'classic' | 'creative'>('modern');

  const testTemplateCreation = async () => {
    setIsLoading(true);
    try {
      const testData = {
        title: `Debug Test - ${selectedTemplate} Template`,
        template_type: selectedTemplate,
        cv_data: {
          personal: {
            name: 'Debug Test User',
            title: 'Template Tester',
            summary: 'Testing template selection functionality'
          },
          contact: {
            email: '<EMAIL>',
            phone: '555-DEBUG',
            location: 'Test City'
          },
          skills: {
            'Testing': ['Template Selection', 'Debugging']
          },
          experience: [],
          education: [],
          projects: [],
          languages: [],
          hobbies: []
        }
      };

      console.log('Sending CV creation request:', testData);
      
      const response = await apiService.createCV(testData);
      
      console.log('CV creation response:', response);
      
      if (response.success && response.data.cv) {
        const createdCV = response.data.cv;
        
        // Test PDF generation
        console.log('Testing PDF generation for CV ID:', createdCV.id);
        const pdfBlob = await apiService.downloadCVPDF(createdCV.id);
        
        setDebugInfo({
          cvCreated: true,
          cvId: createdCV.id,
          requestedTemplate: selectedTemplate,
          storedTemplate: createdCV.template_type,
          templateMatch: createdCV.template_type === selectedTemplate,
          pdfGenerated: true,
          pdfSize: pdfBlob.size,
          response: response
        });
      }
    } catch (error) {
      console.error('Debug test failed:', error);
      setDebugInfo({
        error: error instanceof Error ? error.message : 'Unknown error',
        cvCreated: false
      });
    } finally {
      setIsLoading(false);
    }
  };

  const testExistingCV = async () => {
    if (!cvId) return;
    
    setIsLoading(true);
    try {
      console.log('Fetching CV details for ID:', cvId);
      
      const response = await apiService.getCV(cvId);
      console.log('CV details response:', response);
      
      if (response.success && response.data.cv) {
        const cv = response.data.cv;
        
        // Test PDF generation
        console.log('Testing PDF generation for existing CV ID:', cvId);
        const pdfBlob = await apiService.downloadCVPDF(cvId);
        
        setDebugInfo({
          existingCV: true,
          cvId: cv.id,
          storedTemplate: cv.template_type,
          title: cv.title,
          pdfGenerated: true,
          pdfSize: pdfBlob.size,
          response: response
        });
      }
    } catch (error) {
      console.error('Existing CV test failed:', error);
      setDebugInfo({
        error: error instanceof Error ? error.message : 'Unknown error',
        existingCV: false
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6 mb-6">
      <h3 className="text-lg font-medium text-yellow-800 mb-4">
        🔧 Template Selection Debugger
      </h3>
      
      <div className="space-y-4">
        {/* Template Selection Test */}
        <div>
          <h4 className="font-medium text-yellow-800 mb-2">Test Template Creation</h4>
          <div className="flex items-center space-x-4 mb-2">
            <select
              value={selectedTemplate}
              onChange={(e) => setSelectedTemplate(e.target.value as any)}
              className="border border-yellow-300 rounded px-3 py-1"
            >
              <option value="modern">Modern</option>
              <option value="classic">Classic</option>
              <option value="creative">Creative</option>
            </select>
            <button
              onClick={testTemplateCreation}
              disabled={isLoading}
              className="bg-yellow-600 text-white px-4 py-1 rounded hover:bg-yellow-700 disabled:opacity-50"
            >
              {isLoading ? 'Testing...' : 'Test Create CV'}
            </button>
          </div>
        </div>

        {/* Existing CV Test */}
        {cvId && (
          <div>
            <h4 className="font-medium text-yellow-800 mb-2">Test Existing CV</h4>
            <button
              onClick={testExistingCV}
              disabled={isLoading}
              className="bg-yellow-600 text-white px-4 py-1 rounded hover:bg-yellow-700 disabled:opacity-50"
            >
              {isLoading ? 'Testing...' : `Test CV ID: ${cvId}`}
            </button>
          </div>
        )}

        {/* Debug Results */}
        {debugInfo && (
          <div className="mt-4 p-4 bg-white border border-yellow-300 rounded">
            <h4 className="font-medium text-yellow-800 mb-2">Debug Results:</h4>
            <pre className="text-xs text-gray-700 overflow-auto max-h-64">
              {JSON.stringify(debugInfo, null, 2)}
            </pre>
            
            {debugInfo.templateMatch === false && (
              <div className="mt-2 p-2 bg-red-100 border border-red-300 rounded">
                <p className="text-red-800 font-medium">
                  ⚠️ Template Mismatch Detected!
                </p>
                <p className="text-red-700 text-sm">
                  Requested: {debugInfo.requestedTemplate}, Stored: {debugInfo.storedTemplate}
                </p>
              </div>
            )}
            
            {debugInfo.templateMatch === true && (
              <div className="mt-2 p-2 bg-green-100 border border-green-300 rounded">
                <p className="text-green-800 font-medium">
                  ✅ Template Selection Working Correctly!
                </p>
              </div>
            )}
          </div>
        )}

        <div className="text-sm text-yellow-700">
          <p><strong>Instructions:</strong></p>
          <ol className="list-decimal list-inside space-y-1">
            <li>Open browser developer tools (F12)</li>
            <li>Go to Console tab</li>
            <li>Click "Test Create CV" to test template creation</li>
            <li>Check console logs for detailed request/response data</li>
            <li>Verify the template matches what you selected</li>
          </ol>
        </div>
      </div>
    </div>
  );
};

export default TemplateDebugger;
