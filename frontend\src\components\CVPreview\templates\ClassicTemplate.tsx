import React from 'react';
import { CVData } from '../../../types';

interface ClassicTemplateProps {
  data: CVData;
}

const ClassicTemplate: React.FC<ClassicTemplateProps> = ({ data }) => {
  return (
    <div className="max-w-4xl mx-auto bg-white text-black font-serif leading-relaxed">
      {/* Header */}
      <div className="text-center border-b-2 border-black pb-6 mb-8">
        <h1 className="text-4xl font-bold mb-2 uppercase tracking-wider">
          {data.personal.name || 'Your Name'}
        </h1>
        <h2 className="text-xl italic mb-4">
          {data.personal.title || 'Professional Title'}
        </h2>
        
        {/* Contact Info */}
        <div className="text-sm space-y-1">
          {data.contact.email && <div>{data.contact.email}</div>}
          {data.contact.phone && <div>{data.contact.phone}</div>}
          {data.contact.location && <div>{data.contact.location}</div>}
          {data.contact.linkedin && <div>{data.contact.linkedin}</div>}
        </div>
      </div>

      <div className="px-8">
        {/* Professional Summary */}
        {data.personal.summary && (
          <section className="mb-8">
            <h3 className="text-xl font-bold uppercase tracking-wide border-b border-black pb-1 mb-4">
              Professional Summary
            </h3>
            <p className="text-justify leading-relaxed">
              {data.personal.summary}
            </p>
          </section>
        )}

        {/* Experience */}
        {data.experience.length > 0 && (
          <section className="mb-8">
            <h3 className="text-xl font-bold uppercase tracking-wide border-b border-black pb-1 mb-4">
              Professional Experience
            </h3>
            <div className="space-y-6">
              {data.experience.map((exp, index) => (
                <div key={index} className="border-b border-dotted border-gray-400 pb-4 last:border-b-0">
                  <div className="flex justify-between items-start mb-2">
                    <div>
                      <h4 className="text-lg font-bold">
                        {exp.position}
                      </h4>
                      <p className="italic">
                        {exp.company}
                      </p>
                    </div>
                    <span className="text-sm text-gray-600">
                      {exp.start_date} - {exp.end_date}
                    </span>
                  </div>
                  {exp.location && (
                    <p className="text-sm text-gray-600 mb-2">{exp.location}</p>
                  )}
                  {exp.description && (
                    <p className="text-justify">{exp.description}</p>
                  )}
                </div>
              ))}
            </div>
          </section>
        )}

        {/* Education */}
        {data.education.length > 0 && (
          <section className="mb-8">
            <h3 className="text-xl font-bold uppercase tracking-wide border-b border-black pb-1 mb-4">
              Education
            </h3>
            <div className="space-y-4">
              {data.education.map((edu, index) => (
                <div key={index} className="border-b border-dotted border-gray-400 pb-3 last:border-b-0">
                  <div className="flex justify-between items-start mb-2">
                    <div>
                      <h4 className="text-lg font-bold">
                        {edu.degree}
                      </h4>
                      <p className="italic">
                        {edu.institution}
                      </p>
                    </div>
                    <span className="text-sm text-gray-600">
                      {edu.start_date} - {edu.end_date}
                    </span>
                  </div>
                  {edu.description && (
                    <p className="text-justify">{edu.description}</p>
                  )}
                </div>
              ))}
            </div>
          </section>
        )}

        {/* Skills */}
        {Object.keys(data.skills).length > 0 && (
          <section className="mb-8">
            <h3 className="text-xl font-bold uppercase tracking-wide border-b border-black pb-1 mb-4">
              Skills & Competencies
            </h3>
            <div className="columns-2 gap-8">
              {Object.entries(data.skills).map(([category, skills]) => (
                <div key={category} className="break-inside-avoid mb-4">
                  <h4 className="font-bold underline mb-2">{category}</h4>
                  <ul className="list-none">
                    {skills.map((skill, index) => (
                      <li key={index} className="before:content-['•'] before:mr-2">
                        {skill}
                      </li>
                    ))}
                  </ul>
                </div>
              ))}
            </div>
          </section>
        )}

        {/* Projects */}
        {data.projects.length > 0 && (
          <section className="mb-8">
            <h3 className="text-xl font-bold uppercase tracking-wide border-b border-black pb-1 mb-4">
              Notable Projects
            </h3>
            <div className="space-y-4">
              {data.projects.map((project, index) => (
                <div key={index} className="border-b border-dotted border-gray-400 pb-3 last:border-b-0">
                  <div className="flex justify-between items-start mb-2">
                    <h4 className="text-lg font-bold">
                      {project.name}
                    </h4>
                    {project.date && (
                      <span className="text-sm text-gray-600">
                        {project.date}
                      </span>
                    )}
                  </div>
                  {project.description && (
                    <p className="text-justify mb-2">{project.description}</p>
                  )}
                  {project.technologies && (
                    <p className="text-sm">
                      <strong>Technologies Used:</strong> {project.technologies}
                    </p>
                  )}
                </div>
              ))}
            </div>
          </section>
        )}

        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          {/* Languages */}
          {data.languages.length > 0 && (
            <section>
              <h3 className="text-lg font-bold uppercase tracking-wide border-b border-black pb-1 mb-4">
                Languages
              </h3>
              <div className="space-y-2">
                {data.languages.map((language, index) => (
                  <div key={index}>
                    <strong>{language.name}</strong> ({language.level})
                  </div>
                ))}
              </div>
            </section>
          )}

          {/* Hobbies */}
          {data.hobbies.length > 0 && (
            <section>
              <h3 className="text-lg font-bold uppercase tracking-wide border-b border-black pb-1 mb-4">
                Interests
              </h3>
              <p>
                {data.hobbies.map((hobby, index) => (
                  <span key={index}>
                    {hobby}
                    {index < data.hobbies.length - 1 ? ' • ' : ''}
                  </span>
                ))}
              </p>
            </section>
          )}
        </div>
      </div>
    </div>
  );
};

export default ClassicTemplate;
