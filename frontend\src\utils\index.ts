import { CVData, CVFormStep } from '../types';

// Form validation utilities
export const validateEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

export const validatePhone = (phone: string): boolean => {
  const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
  return phoneRegex.test(phone.replace(/[\s\-\(\)]/g, ''));
};

export const validateUrl = (url: string): boolean => {
  try {
    new URL(url);
    return true;
  } catch {
    return false;
  }
};

// Date utilities
export const formatDate = (date: string): string => {
  if (!date) return '';
  const d = new Date(date);
  return d.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
  });
};

export const formatDateForInput = (date: string): string => {
  if (!date) return '';
  const d = new Date(date);
  return d.toISOString().split('T')[0];
};

export const getCurrentDate = (): string => {
  return new Date().toISOString().split('T')[0];
};

// CV data utilities
export const getEmptyCVData = (): CVData => ({
  personal: {
    name: '',
    title: '',
    summary: '',
  },
  contact: {
    email: '',
    phone: '',
    location: '',
    linkedin: '',
    website: '',
    github: '',
  },
  education: [],
  experience: [],
  projects: [],
  skills: {},
  languages: [],
  hobbies: [],
});

export const generateId = (): string => {
  return Math.random().toString(36).substr(2, 9);
};

// Form step utilities
export const getFormSteps = (): { key: CVFormStep; title: string; description: string }[] => [
  {
    key: 'personal',
    title: 'Personal Information',
    description: 'Basic information about yourself',
  },
  {
    key: 'contact',
    title: 'Contact Details',
    description: 'How employers can reach you',
  },
  {
    key: 'education',
    title: 'Education',
    description: 'Your academic background',
  },
  {
    key: 'experience',
    title: 'Work Experience',
    description: 'Your professional experience',
  },
  {
    key: 'skills',
    title: 'Skills',
    description: 'Your technical and soft skills',
  },
  {
    key: 'projects',
    title: 'Projects',
    description: 'Notable projects you\'ve worked on',
  },
  {
    key: 'languages',
    title: 'Languages',
    description: 'Languages you speak',
  },
  {
    key: 'hobbies',
    title: 'Interests & Hobbies',
    description: 'Your personal interests',
  },
  {
    key: 'preview',
    title: 'Preview',
    description: 'Review your CV before saving',
  },
];

export const getStepIndex = (step: CVFormStep): number => {
  const steps = getFormSteps();
  return steps.findIndex(s => s.key === step);
};

export const getNextStep = (currentStep: CVFormStep): CVFormStep | null => {
  const steps = getFormSteps();
  const currentIndex = getStepIndex(currentStep);
  if (currentIndex < steps.length - 1) {
    return steps[currentIndex + 1].key;
  }
  return null;
};

export const getPreviousStep = (currentStep: CVFormStep): CVFormStep | null => {
  const steps = getFormSteps();
  const currentIndex = getStepIndex(currentStep);
  if (currentIndex > 0) {
    return steps[currentIndex - 1].key;
  }
  return null;
};

// File utilities
export const downloadFile = (blob: Blob, filename: string): void => {
  const url = window.URL.createObjectURL(blob);
  const link = document.createElement('a');
  link.href = url;
  link.download = filename;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  window.URL.revokeObjectURL(url);
};

// String utilities
export const truncateText = (text: string, maxLength: number): string => {
  if (text.length <= maxLength) return text;
  return text.substr(0, maxLength) + '...';
};

export const capitalizeFirst = (str: string): string => {
  return str.charAt(0).toUpperCase() + str.slice(1);
};

export const slugify = (text: string): string => {
  return text
    .toLowerCase()
    .replace(/[^\w ]+/g, '')
    .replace(/ +/g, '-');
};

// Local storage utilities
export const saveToLocalStorage = (key: string, data: any): void => {
  try {
    localStorage.setItem(key, JSON.stringify(data));
  } catch (error) {
    console.error('Error saving to localStorage:', error);
  }
};

export const loadFromLocalStorage = <T>(key: string, defaultValue: T): T => {
  try {
    const item = localStorage.getItem(key);
    return item ? JSON.parse(item) : defaultValue;
  } catch (error) {
    console.error('Error loading from localStorage:', error);
    return defaultValue;
  }
};

export const removeFromLocalStorage = (key: string): void => {
  try {
    localStorage.removeItem(key);
  } catch (error) {
    console.error('Error removing from localStorage:', error);
  }
};

// Debounce utility
export const debounce = <T extends (...args: any[]) => any>(
  func: T,
  wait: number
): ((...args: Parameters<T>) => void) => {
  let timeout: NodeJS.Timeout;
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
};

// Class name utility
export const cn = (...classes: (string | undefined | null | false)[]): string => {
  return classes.filter(Boolean).join(' ');
};
