import React, { useRef, useState, useEffect } from 'react';
import { CVData } from '../../types';
import ModernTemplate from '../CVPreview/templates/ModernTemplate';
import ClassicTemplate from '../CVPreview/templates/ClassicTemplate';
import CreativeTemplate from '../CVPreview/templates/CreativeTemplate';
import { Download, FileText, Loader2, Printer } from 'lucide-react';

interface CVPDFExporterProps {
  data: CVData;
  templateType: 'modern' | 'classic' | 'creative';
  fileName?: string;
  className?: string;
}

const CVPDFExporter: React.FC<CVPDFExporterProps> = ({
  data,
  templateType,
  fileName = 'my-cv',
  className = ''
}) => {
  const [isExporting, setIsExporting] = useState(false);
  const [exportStatus, setExportStatus] = useState<'idle' | 'preparing' | 'generating' | 'complete'>('idle');
  const cvRef = useRef<HTMLDivElement>(null);
  const hiddenRef = useRef<HTMLDivElement>(null);

  // Template component mapping
  const getTemplateComponent = () => {
    const props = { data };
    
    switch (templateType) {
      case 'modern':
        return <ModernTemplate {...props} />;
      case 'classic':
        return <ClassicTemplate {...props} />;
      case 'creative':
        return <CreativeTemplate {...props} />;
      default:
        return <ModernTemplate {...props} />;
    }
  };

  // PDF export configuration
  const getPDFOptions = () => ({
    margin: [0.3, 0.3, 0.3, 0.3], // inches: top, right, bottom, left
    filename: `${fileName}-${templateType}.pdf`,
    image: { 
      type: 'jpeg', 
      quality: 0.95 
    },
    html2canvas: { 
      scale: 2, // Higher scale for better quality
      useCORS: true,
      allowTaint: true,
      backgroundColor: '#ffffff',
      scrollX: 0,
      scrollY: 0,
      width: 794, // A4 width in pixels at 96 DPI
      height: 1123, // A4 height in pixels at 96 DPI
      logging: false
    },
    jsPDF: { 
      unit: 'in', 
      format: 'a4', 
      orientation: 'portrait',
      compress: true
    },
    pagebreak: { 
      mode: ['avoid-all', 'css', 'legacy'],
      before: '.page-break-before',
      after: '.page-break-after',
      avoid: '.page-break-avoid'
    }
  });

  // Browser print function (no dependencies required)
  const printToPDF = async () => {
    if (!hiddenRef.current) return;

    try {
      setIsExporting(true);
      setExportStatus('preparing');

      console.log('🎯 Starting PDF export with template:', templateType);

      // Wait for DOM to be fully updated
      await new Promise(resolve => setTimeout(resolve, 200));

      setExportStatus('generating');

      // Create a new window for printing
      const printWindow = window.open('', '_blank');
      if (!printWindow) {
        throw new Error('Popup blocked. Please allow popups for this site.');
      }

      // Get the template content
      const element = hiddenRef.current;
      const content = element.innerHTML;

      // Create print-optimized HTML
      const printHTML = `
        <!DOCTYPE html>
        <html>
        <head>
          <title>${fileName}-${templateType}</title>
          <style>
            @page {
              size: A4;
              margin: 0.5in;
            }

            body {
              font-family: Arial, sans-serif;
              font-size: 12px;
              line-height: 1.4;
              color: #000;
              background: white;
              margin: 0;
              padding: 0;
            }

            * {
              page-break-inside: avoid !important;
              break-inside: avoid !important;
            }

            .container {
              width: 100%;
              max-width: none;
              margin: 0;
              padding: 20px;
              box-sizing: border-box;
            }

            /* Template-specific styles */
            .header {
              background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%) !important;
              color: white !important;
              padding: 20px !important;
              margin-bottom: 20px !important;
              text-align: center;
            }

            .header h1 {
              font-size: 24px !important;
              margin-bottom: 8px !important;
              color: white !important;
            }

            .header .title {
              font-size: 16px !important;
              margin-bottom: 12px !important;
              color: white !important;
            }

            .contact-info {
              display: flex;
              justify-content: center;
              gap: 15px;
              flex-wrap: wrap;
              font-size: 12px !important;
              color: white !important;
            }

            .section {
              margin-bottom: 15px !important;
            }

            .section h2 {
              font-size: 16px !important;
              color: #2563eb !important;
              border-bottom: 2px solid #2563eb !important;
              padding-bottom: 4px !important;
              margin-bottom: 10px !important;
            }

            .skills-grid {
              display: grid;
              grid-template-columns: 1fr 1fr;
              gap: 10px;
            }

            .skill-category {
              background: #f8f9fa !important;
              padding: 8px !important;
              border-radius: 4px !important;
            }

            .skill-category h3 {
              font-size: 12px !important;
              margin-bottom: 4px !important;
              color: #2563eb !important;
            }

            .experience-item, .education-item, .project-item {
              margin-bottom: 10px !important;
            }

            .experience-item h3, .education-item h3, .project-item h3 {
              font-size: 14px !important;
              margin-bottom: 2px !important;
              font-weight: bold !important;
            }

            .company, .institution {
              font-size: 12px !important;
              color: #666 !important;
              margin-bottom: 4px !important;
            }

            .item-date {
              font-size: 10px !important;
              color: #888 !important;
              margin-bottom: 4px !important;
            }

            p, li, span {
              font-size: 11px !important;
              line-height: 1.3 !important;
              margin-bottom: 4px !important;
            }

            ul {
              margin: 0;
              padding-left: 15px;
            }

            li {
              margin-bottom: 2px !important;
            }
          </style>
        </head>
        <body>
          <div class="container">
            ${content}
          </div>
          <script>
            window.onload = function() {
              window.print();
              setTimeout(() => window.close(), 1000);
            };
          </script>
        </body>
        </html>
      `;

      // Write content to print window
      printWindow.document.write(printHTML);
      printWindow.document.close();

      console.log('✅ Print dialog opened successfully');
      setExportStatus('complete');

      // Reset status after delay
      setTimeout(() => {
        setExportStatus('idle');
      }, 2000);

    } catch (error) {
      console.error('❌ Print export failed:', error);
      setExportStatus('idle');
      alert('Failed to open print dialog. Please try again.');
    } finally {
      setIsExporting(false);
    }
  };

  // Status messages
  const getStatusMessage = () => {
    switch (exportStatus) {
      case 'preparing':
        return 'Preparing template...';
      case 'generating':
        return 'Generating PDF...';
      case 'complete':
        return 'PDF downloaded!';
      default:
        return '';
    }
  };

  return (
    <div className={className}>
      {/* Export Buttons */}
      <div className="flex gap-3">
        <button
          onClick={printToPDF}
          disabled={isExporting}
          className={`
            inline-flex items-center px-4 py-2 rounded-lg font-medium transition-all duration-200
            ${isExporting
              ? 'bg-gray-400 cursor-not-allowed'
              : 'bg-blue-600 hover:bg-blue-700 active:bg-blue-800'
            }
            text-white shadow-sm hover:shadow-md
          `}
        >
          {isExporting ? (
            <Loader2 className="h-4 w-4 mr-2 animate-spin" />
          ) : (
            <Printer className="h-4 w-4 mr-2" />
          )}
          {isExporting ? getStatusMessage() : 'Print to PDF'}
        </button>

        <button
          onClick={() => alert('Install html2pdf.js package first: npm install html2pdf.js')}
          className="inline-flex items-center px-4 py-2 rounded-lg font-medium bg-gray-500 hover:bg-gray-600 text-white shadow-sm"
        >
          <Download className="h-4 w-4 mr-2" />
          Advanced PDF (Requires Package)
        </button>
      </div>

      {/* Template Preview (Visible) */}
      <div 
        ref={cvRef}
        className="mt-6 border border-gray-200 rounded-lg overflow-hidden shadow-sm"
      >
        <div className="bg-gray-50 px-4 py-2 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium text-gray-700">
              Preview: {templateType.charAt(0).toUpperCase() + templateType.slice(1)} Template
            </span>
            <FileText className="h-4 w-4 text-gray-500" />
          </div>
        </div>
        <div className="p-4 bg-white">
          <div className="transform scale-75 origin-top-left" style={{ width: '133.33%' }}>
            {getTemplateComponent()}
          </div>
        </div>
      </div>

      {/* Hidden Template for PDF Export */}
      <div
        ref={hiddenRef}
        style={{
          position: 'fixed',
          left: '-9999px',
          top: '0',
          width: '794px', // A4 width at 96 DPI
          height: 'auto',
          backgroundColor: '#ffffff',
          visibility: 'hidden',
          opacity: '0',
          zIndex: -1
        }}
        className="pdf-export-container"
      >
        <div style={{ 
          width: '794px',
          minHeight: '1123px', // A4 height at 96 DPI
          padding: '40px',
          boxSizing: 'border-box',
          fontFamily: 'Arial, sans-serif',
          fontSize: '14px',
          lineHeight: '1.4',
          color: '#000000'
        }}>
          {getTemplateComponent()}
        </div>
      </div>

      {/* Export Status */}
      {exportStatus !== 'idle' && (
        <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
          <div className="flex items-center">
            {exportStatus === 'complete' ? (
              <div className="flex items-center text-green-700">
                <svg className="h-4 w-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                </svg>
                PDF downloaded successfully!
              </div>
            ) : (
              <div className="flex items-center text-blue-700">
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                {getStatusMessage()}
              </div>
            )}
          </div>
        </div>
      )}

      {/* Debug Info (Remove in production) */}
      {process.env.NODE_ENV === 'development' && (
        <div className="mt-4 p-3 bg-gray-50 border border-gray-200 rounded text-xs text-gray-600">
          <strong>Debug Info:</strong><br />
          Template: {templateType}<br />
          Export Status: {exportStatus}<br />
          Is Exporting: {isExporting.toString()}
        </div>
      )}
    </div>
  );
};

export default CVPDFExporter;
