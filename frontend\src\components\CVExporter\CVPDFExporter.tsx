import React, { useRef, useState, useEffect } from 'react';
import html2pdf from 'html2pdf.js';
import { CVData } from '../../types';
import ModernTemplate from '../CVPreview/templates/ModernTemplate';
import ClassicTemplate from '../CVPreview/templates/ClassicTemplate';
import CreativeTemplate from '../CVPreview/templates/CreativeTemplate';
import { Download, FileText, Loader2 } from 'lucide-react';

interface CVPDFExporterProps {
  data: CVData;
  templateType: 'modern' | 'classic' | 'creative';
  fileName?: string;
  className?: string;
}

const CVPDFExporter: React.FC<CVPDFExporterProps> = ({
  data,
  templateType,
  fileName = 'my-cv',
  className = ''
}) => {
  const [isExporting, setIsExporting] = useState(false);
  const [exportStatus, setExportStatus] = useState<'idle' | 'preparing' | 'generating' | 'complete'>('idle');
  const cvRef = useRef<HTMLDivElement>(null);
  const hiddenRef = useRef<HTMLDivElement>(null);

  // Template component mapping
  const getTemplateComponent = () => {
    const props = { data };
    
    switch (templateType) {
      case 'modern':
        return <ModernTemplate {...props} />;
      case 'classic':
        return <ClassicTemplate {...props} />;
      case 'creative':
        return <CreativeTemplate {...props} />;
      default:
        return <ModernTemplate {...props} />;
    }
  };

  // PDF export configuration
  const getPDFOptions = () => ({
    margin: [0.3, 0.3, 0.3, 0.3], // inches: top, right, bottom, left
    filename: `${fileName}-${templateType}.pdf`,
    image: { 
      type: 'jpeg', 
      quality: 0.95 
    },
    html2canvas: { 
      scale: 2, // Higher scale for better quality
      useCORS: true,
      allowTaint: true,
      backgroundColor: '#ffffff',
      scrollX: 0,
      scrollY: 0,
      width: 794, // A4 width in pixels at 96 DPI
      height: 1123, // A4 height in pixels at 96 DPI
      logging: false
    },
    jsPDF: { 
      unit: 'in', 
      format: 'a4', 
      orientation: 'portrait',
      compress: true
    },
    pagebreak: { 
      mode: ['avoid-all', 'css', 'legacy'],
      before: '.page-break-before',
      after: '.page-break-after',
      avoid: '.page-break-avoid'
    }
  });

  // Export function with proper DOM handling
  const exportToPDF = async () => {
    if (!hiddenRef.current) return;

    try {
      setIsExporting(true);
      setExportStatus('preparing');

      console.log('🎯 Starting PDF export with template:', templateType);

      // Wait for DOM to be fully updated
      await new Promise(resolve => setTimeout(resolve, 100));

      setExportStatus('generating');

      // Get the hidden element that contains the current template
      const element = hiddenRef.current;
      
      // Ensure the element is visible for html2canvas
      element.style.position = 'absolute';
      element.style.left = '-9999px';
      element.style.top = '0';
      element.style.visibility = 'visible';
      element.style.opacity = '1';

      console.log('📄 Generating PDF from element:', element);
      console.log('🎨 Template type:', templateType);

      // Generate PDF
      await html2pdf()
        .set(getPDFOptions())
        .from(element)
        .save();

      console.log('✅ PDF export completed successfully');
      setExportStatus('complete');

      // Reset status after delay
      setTimeout(() => {
        setExportStatus('idle');
      }, 2000);

    } catch (error) {
      console.error('❌ PDF export failed:', error);
      setExportStatus('idle');
      alert('Failed to export PDF. Please try again.');
    } finally {
      setIsExporting(false);
    }
  };

  // Status messages
  const getStatusMessage = () => {
    switch (exportStatus) {
      case 'preparing':
        return 'Preparing template...';
      case 'generating':
        return 'Generating PDF...';
      case 'complete':
        return 'PDF downloaded!';
      default:
        return '';
    }
  };

  return (
    <div className={className}>
      {/* Export Button */}
      <button
        onClick={exportToPDF}
        disabled={isExporting}
        className={`
          inline-flex items-center px-4 py-2 rounded-lg font-medium transition-all duration-200
          ${isExporting 
            ? 'bg-gray-400 cursor-not-allowed' 
            : 'bg-blue-600 hover:bg-blue-700 active:bg-blue-800'
          }
          text-white shadow-sm hover:shadow-md
        `}
      >
        {isExporting ? (
          <Loader2 className="h-4 w-4 mr-2 animate-spin" />
        ) : (
          <Download className="h-4 w-4 mr-2" />
        )}
        {isExporting ? getStatusMessage() : 'Download PDF'}
      </button>

      {/* Template Preview (Visible) */}
      <div 
        ref={cvRef}
        className="mt-6 border border-gray-200 rounded-lg overflow-hidden shadow-sm"
      >
        <div className="bg-gray-50 px-4 py-2 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium text-gray-700">
              Preview: {templateType.charAt(0).toUpperCase() + templateType.slice(1)} Template
            </span>
            <FileText className="h-4 w-4 text-gray-500" />
          </div>
        </div>
        <div className="p-4 bg-white">
          <div className="transform scale-75 origin-top-left" style={{ width: '133.33%' }}>
            {getTemplateComponent()}
          </div>
        </div>
      </div>

      {/* Hidden Template for PDF Export */}
      <div
        ref={hiddenRef}
        style={{
          position: 'fixed',
          left: '-9999px',
          top: '0',
          width: '794px', // A4 width at 96 DPI
          height: 'auto',
          backgroundColor: '#ffffff',
          visibility: 'hidden',
          opacity: '0',
          zIndex: -1
        }}
        className="pdf-export-container"
      >
        <div style={{ 
          width: '794px',
          minHeight: '1123px', // A4 height at 96 DPI
          padding: '40px',
          boxSizing: 'border-box',
          fontFamily: 'Arial, sans-serif',
          fontSize: '14px',
          lineHeight: '1.4',
          color: '#000000'
        }}>
          {getTemplateComponent()}
        </div>
      </div>

      {/* Export Status */}
      {exportStatus !== 'idle' && (
        <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
          <div className="flex items-center">
            {exportStatus === 'complete' ? (
              <div className="flex items-center text-green-700">
                <svg className="h-4 w-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                </svg>
                PDF downloaded successfully!
              </div>
            ) : (
              <div className="flex items-center text-blue-700">
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                {getStatusMessage()}
              </div>
            )}
          </div>
        </div>
      )}

      {/* Debug Info (Remove in production) */}
      {process.env.NODE_ENV === 'development' && (
        <div className="mt-4 p-3 bg-gray-50 border border-gray-200 rounded text-xs text-gray-600">
          <strong>Debug Info:</strong><br />
          Template: {templateType}<br />
          Export Status: {exportStatus}<br />
          Is Exporting: {isExporting.toString()}
        </div>
      )}
    </div>
  );
};

export default CVPDFExporter;
