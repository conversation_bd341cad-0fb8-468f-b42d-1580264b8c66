import React, { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import { CV } from '../types';
import { apiService, handleApiError } from '../services/api';
import CVBuilderWizard from '../components/CVBuilder/CVBuilderWizard';
import LoadingSpinner from '../components/LoadingSpinner';

const CVEditPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const [cv, setCv] = useState<CV | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState('');

  useEffect(() => {
    if (id) {
      fetchCV();
    }
  }, [id]);

  const fetchCV = async () => {
    try {
      setIsLoading(true);
      const response = await apiService.getCV(Number(id));
      if (response.success && response.data.cv) {
        setCv(response.data.cv);
      }
    } catch (err) {
      setError(handleApiError(err));
    } finally {
      setIsLoading(false);
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  if (error || !cv) {
    return (
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="text-center">
          <div className="rounded-md bg-red-50 dark:bg-red-900/50 p-4">
            <p className="text-sm text-red-800 dark:text-red-200">
              {error || 'CV not found'}
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <CVBuilderWizard
      initialData={cv.cv_data}
      cvId={cv.id}
      onSave={(updatedCV) => {
        setCv(updatedCV);
      }}
    />
  );
};

export default CVEditPage;
