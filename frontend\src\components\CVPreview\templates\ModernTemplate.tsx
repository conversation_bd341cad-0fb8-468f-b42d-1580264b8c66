import React from 'react';
import { CVData } from '../../../types';
import { Mail, Phone, MapPin, Linkedin, Globe, Github } from 'lucide-react';

interface ModernTemplateProps {
  data: CVData;
}

const ModernTemplate: React.FC<ModernTemplateProps> = ({ data }) => {
  return (
    <div className="max-w-4xl mx-auto bg-white text-gray-900 font-sans leading-relaxed">
      {/* Header */}
      <div className="bg-gradient-to-r from-blue-600 to-blue-800 text-white p-8">
        <div className="text-center">
          <h1 className="text-4xl font-bold mb-2">
            {data.personal.name || 'Your Name'}
          </h1>
          <h2 className="text-xl font-light mb-4 opacity-90">
            {data.personal.title || 'Professional Title'}
          </h2>
          
          {/* Contact Info */}
          <div className="flex flex-wrap justify-center gap-4 text-sm">
            {data.contact.email && (
              <div className="flex items-center">
                <Mail className="h-4 w-4 mr-1" />
                {data.contact.email}
              </div>
            )}
            {data.contact.phone && (
              <div className="flex items-center">
                <Phone className="h-4 w-4 mr-1" />
                {data.contact.phone}
              </div>
            )}
            {data.contact.location && (
              <div className="flex items-center">
                <MapPin className="h-4 w-4 mr-1" />
                {data.contact.location}
              </div>
            )}
            {data.contact.linkedin && (
              <div className="flex items-center">
                <Linkedin className="h-4 w-4 mr-1" />
                LinkedIn
              </div>
            )}
            {data.contact.website && (
              <div className="flex items-center">
                <Globe className="h-4 w-4 mr-1" />
                Website
              </div>
            )}
            {data.contact.github && (
              <div className="flex items-center">
                <Github className="h-4 w-4 mr-1" />
                GitHub
              </div>
            )}
          </div>
        </div>
      </div>

      <div className="p-8">
        {/* Professional Summary */}
        {data.personal.summary && (
          <section className="mb-8">
            <h3 className="text-2xl font-bold text-blue-800 border-b-2 border-blue-200 pb-2 mb-4">
              Professional Summary
            </h3>
            <p className="text-gray-700 leading-relaxed">
              {data.personal.summary}
            </p>
          </section>
        )}

        {/* Experience */}
        {data.experience.length > 0 && (
          <section className="mb-8">
            <h3 className="text-2xl font-bold text-blue-800 border-b-2 border-blue-200 pb-2 mb-4">
              Work Experience
            </h3>
            <div className="space-y-6">
              {data.experience.map((exp, index) => (
                <div key={index} className="border-l-4 border-blue-300 pl-6">
                  <div className="flex justify-between items-start mb-2">
                    <div>
                      <h4 className="text-lg font-semibold text-gray-900">
                        {exp.position}
                      </h4>
                      <p className="text-blue-600 font-medium">
                        {exp.company}
                      </p>
                    </div>
                    <span className="text-sm text-gray-500 bg-gray-100 px-3 py-1 rounded">
                      {exp.start_date} - {exp.end_date}
                    </span>
                  </div>
                  {exp.location && (
                    <p className="text-sm text-gray-500 mb-2">{exp.location}</p>
                  )}
                  {exp.description && (
                    <p className="text-gray-700">{exp.description}</p>
                  )}
                </div>
              ))}
            </div>
          </section>
        )}

        {/* Education */}
        {data.education.length > 0 && (
          <section className="mb-8">
            <h3 className="text-2xl font-bold text-blue-800 border-b-2 border-blue-200 pb-2 mb-4">
              Education
            </h3>
            <div className="space-y-4">
              {data.education.map((edu, index) => (
                <div key={index} className="border-l-4 border-blue-300 pl-6">
                  <div className="flex justify-between items-start mb-2">
                    <div>
                      <h4 className="text-lg font-semibold text-gray-900">
                        {edu.degree}
                      </h4>
                      <p className="text-blue-600 font-medium">
                        {edu.institution}
                      </p>
                    </div>
                    <span className="text-sm text-gray-500 bg-gray-100 px-3 py-1 rounded">
                      {edu.start_date} - {edu.end_date}
                    </span>
                  </div>
                  {edu.description && (
                    <p className="text-gray-700">{edu.description}</p>
                  )}
                </div>
              ))}
            </div>
          </section>
        )}

        {/* Skills */}
        {Object.keys(data.skills).length > 0 && (
          <section className="mb-8">
            <h3 className="text-2xl font-bold text-blue-800 border-b-2 border-blue-200 pb-2 mb-4">
              Skills & Competencies
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {Object.entries(data.skills).map(([category, skills]) => (
                <div key={category} className="bg-gray-50 p-4 rounded-lg">
                  <h4 className="font-semibold text-gray-900 mb-2">{category}</h4>
                  <div className="flex flex-wrap gap-2">
                    {skills.map((skill, index) => (
                      <span
                        key={index}
                        className="bg-blue-100 text-blue-800 px-2 py-1 rounded text-sm"
                      >
                        {skill}
                      </span>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          </section>
        )}

        {/* Projects */}
        {data.projects.length > 0 && (
          <section className="mb-8">
            <h3 className="text-2xl font-bold text-blue-800 border-b-2 border-blue-200 pb-2 mb-4">
              Notable Projects
            </h3>
            <div className="space-y-4">
              {data.projects.map((project, index) => (
                <div key={index} className="border-l-4 border-blue-300 pl-6">
                  <div className="flex justify-between items-start mb-2">
                    <h4 className="text-lg font-semibold text-gray-900">
                      {project.name}
                    </h4>
                    {project.date && (
                      <span className="text-sm text-gray-500 bg-gray-100 px-3 py-1 rounded">
                        {project.date}
                      </span>
                    )}
                  </div>
                  {project.description && (
                    <p className="text-gray-700 mb-2">{project.description}</p>
                  )}
                  {project.technologies && (
                    <p className="text-sm text-gray-600">
                      <strong>Technologies:</strong> {project.technologies}
                    </p>
                  )}
                </div>
              ))}
            </div>
          </section>
        )}

        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          {/* Languages */}
          {data.languages.length > 0 && (
            <section>
              <h3 className="text-xl font-bold text-blue-800 border-b-2 border-blue-200 pb-2 mb-4">
                Languages
              </h3>
              <div className="space-y-2">
                {data.languages.map((language, index) => (
                  <div key={index} className="flex justify-between">
                    <span className="font-medium">{language.name}</span>
                    <span className="text-blue-600">{language.level}</span>
                  </div>
                ))}
              </div>
            </section>
          )}

          {/* Hobbies */}
          {data.hobbies.length > 0 && (
            <section>
              <h3 className="text-xl font-bold text-blue-800 border-b-2 border-blue-200 pb-2 mb-4">
                Interests & Hobbies
              </h3>
              <div className="flex flex-wrap gap-2">
                {data.hobbies.map((hobby, index) => (
                  <span
                    key={index}
                    className="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm"
                  >
                    {hobby}
                  </span>
                ))}
              </div>
            </section>
          )}
        </div>
      </div>
    </div>
  );
};

export default ModernTemplate;
