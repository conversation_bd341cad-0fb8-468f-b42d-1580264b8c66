import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { AuthProvider } from './contexts/AuthContext';
import { ThemeProvider } from './contexts/ThemeContext';
import ProtectedRoute from './components/ProtectedRoute';
import Layout from './components/Layout';
import HomePage from './pages/HomePage';
import LoginPage from './pages/LoginPage';
import RegisterPage from './pages/RegisterPage';
import DashboardPage from './pages/DashboardPage';
import CVBuilderPage from './pages/CVBuilderPage';
import CVEditPage from './pages/CVEditPage';
import CVPreviewPage from './components/CVPreview/CVPreviewPage';
import CVPreviewPage from './pages/CVPreviewPage';

function App() {
  return (
    <ThemeProvider>
      <AuthProvider>
        <Router>
          <div className="min-h-screen bg-gray-50 dark:bg-gray-900 text-gray-900 dark:text-white">
            <Routes>
              {/* Public routes */}
              <Route path="/" element={<HomePage />} />
              <Route path="/login" element={<LoginPage />} />
              <Route path="/register" element={<RegisterPage />} />

              {/* Protected routes */}
              <Route
                path="/dashboard"
                element={
                  <ProtectedRoute>
                    <Layout>
                      <DashboardPage />
                    </Layout>
                  </ProtectedRoute>
                }
              />
              <Route
                path="/cv/new"
                element={
                  <ProtectedRoute>
                    <Layout>
                      <CVBuilderPage />
                    </Layout>
                  </ProtectedRoute>
                }
              />
              <Route
                path="/cv/:id/edit"
                element={
                  <ProtectedRoute>
                    <Layout>
                      <CVEditPage />
                    </Layout>
                  </ProtectedRoute>
                }
              />
              <Route
                path="/cv/:id/preview"
                element={
                  <ProtectedRoute>
                    <Layout>
                      <CVPreviewPage />
                    </Layout>
                  </ProtectedRoute>
                }
              />

              {/* Catch all route */}
              <Route path="*" element={<Navigate to="/" replace />} />
            </Routes>
          </div>
        </Router>
      </AuthProvider>
    </ThemeProvider>
  );
}

export default App;
