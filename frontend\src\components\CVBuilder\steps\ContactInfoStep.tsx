import React from 'react';
import { useForm } from 'react-hook-form';
import { FormStepProps, ContactInfo } from '../../../types';
import { validateEmail, validatePhone, validateUrl } from '../../../utils';
import { Mail, Phone, MapPin, Linkedin, Globe, Github } from 'lucide-react';

const ContactInfoStep: React.FC<FormStepProps> = ({ data, updateData, onNext, onPrevious }) => {
  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<ContactInfo>({
    defaultValues: data.contact,
  });

  const onSubmit = (formData: ContactInfo) => {
    updateData('contact', formData);
    onNext();
  };

  return (
    <div>
      <div className="flex items-center mb-6">
        <Mail className="h-6 w-6 text-primary-600 mr-3" />
        <div>
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
            Contact Information
          </h2>
          <p className="text-gray-600 dark:text-gray-400">
            How can employers reach you?
          </p>
        </div>
      </div>

      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label htmlFor="email" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
              <Mail className="inline h-4 w-4 mr-1" />
              Email Address *
            </label>
            <input
              {...register('email', {
                required: 'Email is required',
                validate: (value) => validateEmail(value) || 'Please enter a valid email address',
              })}
              type="email"
              className="mt-1 input"
              placeholder="<EMAIL>"
            />
            {errors.email && (
              <p className="mt-1 text-sm text-red-600 dark:text-red-400">
                {errors.email.message}
              </p>
            )}
          </div>

          <div>
            <label htmlFor="phone" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
              <Phone className="inline h-4 w-4 mr-1" />
              Phone Number *
            </label>
            <input
              {...register('phone', {
                required: 'Phone number is required',
                validate: (value) => validatePhone(value) || 'Please enter a valid phone number',
              })}
              type="tel"
              className="mt-1 input"
              placeholder="+****************"
            />
            {errors.phone && (
              <p className="mt-1 text-sm text-red-600 dark:text-red-400">
                {errors.phone.message}
              </p>
            )}
          </div>
        </div>

        <div>
          <label htmlFor="location" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
            <MapPin className="inline h-4 w-4 mr-1" />
            Location *
          </label>
          <input
            {...register('location', {
              required: 'Location is required',
              minLength: {
                value: 2,
                message: 'Location must be at least 2 characters',
              },
            })}
            type="text"
            className="mt-1 input"
            placeholder="City, State/Country"
          />
          {errors.location && (
            <p className="mt-1 text-sm text-red-600 dark:text-red-400">
              {errors.location.message}
            </p>
          )}
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label htmlFor="linkedin" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
              <Linkedin className="inline h-4 w-4 mr-1" />
              LinkedIn Profile
            </label>
            <input
              {...register('linkedin', {
                validate: (value) => !value || validateUrl(value) || 'Please enter a valid LinkedIn URL',
              })}
              type="url"
              className="mt-1 input"
              placeholder="https://linkedin.com/in/yourprofile"
            />
            {errors.linkedin && (
              <p className="mt-1 text-sm text-red-600 dark:text-red-400">
                {errors.linkedin.message}
              </p>
            )}
          </div>

          <div>
            <label htmlFor="website" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
              <Globe className="inline h-4 w-4 mr-1" />
              Personal Website
            </label>
            <input
              {...register('website', {
                validate: (value) => !value || validateUrl(value) || 'Please enter a valid website URL',
              })}
              type="url"
              className="mt-1 input"
              placeholder="https://yourwebsite.com"
            />
            {errors.website && (
              <p className="mt-1 text-sm text-red-600 dark:text-red-400">
                {errors.website.message}
              </p>
            )}
          </div>
        </div>

        <div>
          <label htmlFor="github" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
            <Github className="inline h-4 w-4 mr-1" />
            GitHub Profile
          </label>
          <input
            {...register('github', {
              validate: (value) => !value || validateUrl(value) || 'Please enter a valid GitHub URL',
            })}
            type="url"
            className="mt-1 input"
            placeholder="https://github.com/yourusername"
          />
          {errors.github && (
            <p className="mt-1 text-sm text-red-600 dark:text-red-400">
              {errors.github.message}
            </p>
          )}
        </div>

        <div className="flex justify-between">
          <button type="button" onClick={onPrevious} className="btn-outline">
            Previous
          </button>
          <button type="submit" className="btn-primary">
            Next: Education
          </button>
        </div>
      </form>
    </div>
  );
};

export default ContactInfoStep;
