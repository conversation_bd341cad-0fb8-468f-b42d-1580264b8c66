<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class CV extends Model
{
    protected $table = 'cvs';

    protected $fillable = [
        'user_id',
        'title',
        'cv_data',
        'template_type',
    ];

    protected $casts = [
        'cv_data' => 'array',
    ];

    /**
     * Get the user that owns the CV.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }
}
