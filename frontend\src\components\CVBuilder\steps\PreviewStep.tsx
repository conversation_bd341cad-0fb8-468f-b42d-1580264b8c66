import React from 'react';
import { FormStepProps } from '../../../types';
import { Eye, Save, AlertCircle } from 'lucide-react';
import LoadingSpinner from '../../LoadingSpinner';
import CVPreview from '../../CVPreview/CVPreview';
import TemplateDebugger from '../../TemplateDebugger';

interface PreviewStepProps extends FormStepProps {
  cvTitle: string;
  setCvTitle: (title: string) => void;
  templateType: 'modern' | 'classic' | 'creative';
  setTemplateType: (type: 'modern' | 'classic' | 'creative') => void;
  onSave: () => void;
  isLoading: boolean;
  error: string;
}

const PreviewStep: React.FC<PreviewStepProps> = ({
  data,
  onPrevious,
  cvTitle,
  setCvTitle,
  templateType,
  setTemplateType,
  onSave,
  isLoading,
  error,
}) => {
  const templates = [
    {
      id: 'modern' as const,
      name: 'Modern',
      description: 'Clean and contemporary design with blue accents',
      preview: '🎨 Modern layout with clean lines and professional styling'
    },
    {
      id: 'classic' as const,
      name: 'Classic',
      description: 'Traditional and timeless design',
      preview: '📄 Traditional layout with serif fonts and formal styling'
    },
    {
      id: 'creative' as const,
      name: 'Creative',
      description: 'Eye-catching design with gradients and modern elements',
      preview: '✨ Creative layout with gradients and modern visual elements'
    }
  ];

  return (
    <div>
      <div className="flex items-center mb-6">
        <Eye className="h-6 w-6 text-primary-600 mr-3" />
        <div>
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
            Preview & Save
          </h2>
          <p className="text-gray-600 dark:text-gray-400">
            Review your CV, choose a template, and save your work
          </p>
        </div>
      </div>

      {/* Template Debugger - Remove this in production */}
      <TemplateDebugger />

      {/* Error Message */}
      {error && (
        <div className="mb-6 rounded-md bg-red-50 dark:bg-red-900/50 p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <AlertCircle className="h-5 w-5 text-red-400" />
            </div>
            <div className="ml-3">
              <p className="text-sm text-red-800 dark:text-red-200">{error}</p>
            </div>
          </div>
        </div>
      )}

      {/* CV Title */}
      <div className="mb-6">
        <label htmlFor="cvTitle" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          CV Title *
        </label>
        <input
          type="text"
          id="cvTitle"
          value={cvTitle}
          onChange={(e) => setCvTitle(e.target.value)}
          placeholder="e.g., John Doe - Software Engineer"
          className="input"
          required
        />
        <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
          This will be the filename when you download your CV
        </p>
      </div>

      {/* Template Selection */}
      <div className="mb-6">
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
          Choose Template
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {templates.map((template) => (
            <div
              key={template.id}
              className={`relative rounded-lg border-2 cursor-pointer transition-colors ${
                templateType === template.id
                  ? 'border-primary-500 bg-primary-50 dark:bg-primary-900/20'
                  : 'border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600'
              }`}
              onClick={() => setTemplateType(template.id)}
            >
              <div className="p-4">
                <div className="flex items-center justify-between mb-2">
                  <h4 className="text-lg font-medium text-gray-900 dark:text-white">
                    {template.name}
                  </h4>
                  {templateType === template.id && (
                    <div className="w-4 h-4 bg-primary-500 rounded-full flex items-center justify-center">
                      <div className="w-2 h-2 bg-white rounded-full"></div>
                    </div>
                  )}
                </div>
                <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">
                  {template.description}
                </p>
                <div className="bg-gray-100 dark:bg-gray-700 rounded p-3 text-sm text-gray-600 dark:text-gray-300">
                  {template.preview}
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* CV Preview */}
      <div className="mb-6">
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
          CV Preview
        </h3>
        <div className="border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden">
          <div className="max-h-96 overflow-y-auto">
            <CVPreview data={data} template={templateType} className="scale-75 origin-top" />
          </div>
        </div>
      </div>

      {/* CV Summary */}
      <div className="mb-6 bg-gray-50 dark:bg-gray-700 rounded-lg p-6">
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
          CV Summary
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
          <div>
            <p className="text-gray-600 dark:text-gray-400">
              <strong>Name:</strong> {data.personal.name || 'Not provided'}
            </p>
            <p className="text-gray-600 dark:text-gray-400">
              <strong>Title:</strong> {data.personal.title || 'Not provided'}
            </p>
            <p className="text-gray-600 dark:text-gray-400">
              <strong>Email:</strong> {data.contact.email || 'Not provided'}
            </p>
            <p className="text-gray-600 dark:text-gray-400">
              <strong>Phone:</strong> {data.contact.phone || 'Not provided'}
            </p>
          </div>
          <div>
            <p className="text-gray-600 dark:text-gray-400">
              <strong>Education entries:</strong> {data.education.length}
            </p>
            <p className="text-gray-600 dark:text-gray-400">
              <strong>Work experiences:</strong> {data.experience.length}
            </p>
            <p className="text-gray-600 dark:text-gray-400">
              <strong>Projects:</strong> {data.projects.length}
            </p>
            <p className="text-gray-600 dark:text-gray-400">
              <strong>Languages:</strong> {data.languages.length}
            </p>
          </div>
        </div>
      </div>

      {/* Actions */}
      <div className="flex justify-between items-center">
        <button type="button" onClick={onPrevious} className="btn-outline">
          Previous
        </button>
        
        <div className="flex space-x-3">
          <button
            onClick={onSave}
            disabled={isLoading || !cvTitle.trim()}
            className="btn-primary disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
          >
            {isLoading ? (
              <>
                <LoadingSpinner size="sm" className="mr-2" />
                Saving...
              </>
            ) : (
              <>
                <Save className="h-4 w-4 mr-2" />
                Save CV
              </>
            )}
          </button>
        </div>
      </div>

      {/* Tips */}
      <div className="mt-6 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
        <h4 className="text-sm font-medium text-blue-900 dark:text-blue-200 mb-2">
          💡 Tips for a great CV:
        </h4>
        <ul className="text-sm text-blue-800 dark:text-blue-300 space-y-1">
          <li>• Keep your professional summary concise and impactful</li>
          <li>• Use action verbs to describe your achievements</li>
          <li>• Quantify your accomplishments with numbers when possible</li>
          <li>• Tailor your CV for each job application</li>
          <li>• Proofread carefully for spelling and grammar errors</li>
        </ul>
      </div>
    </div>
  );
};

export default PreviewStep;
