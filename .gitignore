# Dependencies
node_modules/
vendor/

# Production builds
/frontend/dist/
/frontend/build/

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Laravel specific
/backend/bootstrap/cache/
/backend/storage/app/public/
/backend/storage/framework/cache/
/backend/storage/framework/sessions/
/backend/storage/framework/testing/
/backend/storage/framework/views/
/backend/storage/logs/
/backend/public/storage

# IDE and editor files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# Dependency directories
jspm_packages/

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# next.js build output
.next

# nuxt.js build output
.nuxt

# vuepress build output
.vuepress/dist

# Serverless directories
.serverless/

# FuseBox cache
.fusebox/

# DynamoDB Local files
.dynamodb/

# TernJS port file
.tern-port

# Stores VSCode versions used for testing VSCode extensions
.vscode-test

# Laravel Mix
/backend/public/hot
/backend/public/storage
/backend/storage/*.key

# Composer
/backend/composer.lock

# Package Lock Files
package-lock.json
yarn.lock

# Database
*.sqlite
*.sqlite3

# Temporary files
*.tmp
*.temp
