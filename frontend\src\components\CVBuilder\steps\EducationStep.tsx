import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { FormStepProps, Education } from '../../../types';
import { generateId } from '../../../utils';
import { GraduationCap, Plus, Trash2, Edit } from 'lucide-react';

const EducationStep: React.FC<FormStepProps> = ({ data, updateData, onNext, onPrevious }) => {
  const [educationList, setEducationList] = useState<Education[]>(data.education || []);
  const [editingIndex, setEditingIndex] = useState<number | null>(null);
  const [showForm, setShowForm] = useState(false);

  const {
    register,
    handleSubmit,
    reset,
    formState: { errors },
  } = useForm<Education>();

  const onSubmit = (formData: Education) => {
    const newEducation = { ...formData, id: generateId() };
    
    if (editingIndex !== null) {
      // Update existing education
      const updated = [...educationList];
      updated[editingIndex] = newEducation;
      setEducationList(updated);
      setEditingIndex(null);
    } else {
      // Add new education
      setEducationList([...educationList, newEducation]);
    }
    
    reset();
    setShowForm(false);
  };

  const handleEdit = (index: number) => {
    const education = educationList[index];
    reset(education);
    setEditingIndex(index);
    setShowForm(true);
  };

  const handleDelete = (index: number) => {
    const updated = educationList.filter((_, i) => i !== index);
    setEducationList(updated);
  };

  const handleNext = () => {
    updateData('education', educationList);
    onNext();
  };

  const cancelEdit = () => {
    reset();
    setEditingIndex(null);
    setShowForm(false);
  };

  return (
    <div>
      <div className="flex items-center mb-6">
        <GraduationCap className="h-6 w-6 text-primary-600 mr-3" />
        <div>
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
            Education
          </h2>
          <p className="text-gray-600 dark:text-gray-400">
            Add your educational background and qualifications
          </p>
        </div>
      </div>

      {/* Education List */}
      {educationList.length > 0 && (
        <div className="mb-6 space-y-4">
          {educationList.map((education, index) => (
            <div key={education.id} className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
              <div className="flex justify-between items-start">
                <div className="flex-1">
                  <h3 className="font-semibold text-gray-900 dark:text-white">
                    {education.degree}
                  </h3>
                  <p className="text-gray-600 dark:text-gray-300">
                    {education.institution}
                  </p>
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    {education.start_date} - {education.end_date}
                  </p>
                  {education.description && (
                    <p className="mt-2 text-sm text-gray-600 dark:text-gray-300">
                      {education.description}
                    </p>
                  )}
                </div>
                <div className="flex space-x-2 ml-4">
                  <button
                    onClick={() => handleEdit(index)}
                    className="p-2 text-gray-500 hover:text-primary-600"
                  >
                    <Edit className="h-4 w-4" />
                  </button>
                  <button
                    onClick={() => handleDelete(index)}
                    className="p-2 text-gray-500 hover:text-red-600"
                  >
                    <Trash2 className="h-4 w-4" />
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Add Education Button */}
      {!showForm && (
        <button
          onClick={() => setShowForm(true)}
          className="mb-6 inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600"
        >
          <Plus className="h-4 w-4 mr-2" />
          Add Education
        </button>
      )}

      {/* Education Form */}
      {showForm && (
        <form onSubmit={handleSubmit(onSubmit)} className="mb-6 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-6">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
            {editingIndex !== null ? 'Edit Education' : 'Add Education'}
          </h3>
          
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Degree/Qualification *
              </label>
              <input
                {...register('degree', { required: 'Degree is required' })}
                type="text"
                className="mt-1 input"
                placeholder="e.g., Bachelor of Science in Computer Science"
              />
              {errors.degree && (
                <p className="mt-1 text-sm text-red-600 dark:text-red-400">
                  {errors.degree.message}
                </p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Institution *
              </label>
              <input
                {...register('institution', { required: 'Institution is required' })}
                type="text"
                className="mt-1 input"
                placeholder="e.g., University of Technology"
              />
              {errors.institution && (
                <p className="mt-1 text-sm text-red-600 dark:text-red-400">
                  {errors.institution.message}
                </p>
              )}
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  Start Date *
                </label>
                <input
                  {...register('start_date', { required: 'Start date is required' })}
                  type="month"
                  className="mt-1 input"
                />
                {errors.start_date && (
                  <p className="mt-1 text-sm text-red-600 dark:text-red-400">
                    {errors.start_date.message}
                  </p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  End Date *
                </label>
                <input
                  {...register('end_date', { required: 'End date is required' })}
                  type="month"
                  className="mt-1 input"
                />
                {errors.end_date && (
                  <p className="mt-1 text-sm text-red-600 dark:text-red-400">
                    {errors.end_date.message}
                  </p>
                )}
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Description
              </label>
              <textarea
                {...register('description')}
                rows={3}
                className="mt-1 textarea"
                placeholder="Relevant coursework, achievements, honors, etc."
              />
            </div>
          </div>

          <div className="flex justify-end space-x-3 mt-6">
            <button type="button" onClick={cancelEdit} className="btn-outline">
              Cancel
            </button>
            <button type="submit" className="btn-primary">
              {editingIndex !== null ? 'Update' : 'Add'} Education
            </button>
          </div>
        </form>
      )}

      {/* Navigation */}
      <div className="flex justify-between">
        <button type="button" onClick={onPrevious} className="btn-outline">
          Previous
        </button>
        <button type="button" onClick={handleNext} className="btn-primary">
          Next: Experience
        </button>
      </div>
    </div>
  );
};

export default EducationStep;
