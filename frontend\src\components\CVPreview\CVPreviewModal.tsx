import React, { useState, useRef } from 'react';
import { CVData } from '../../types';
import ModernTemplate from './templates/ModernTemplate';
import ClassicTemplate from './templates/ClassicTemplate';
import CreativeTemplate from './templates/CreativeTemplate';
import { X, Download, Printer, ZoomIn, ZoomOut, RotateCcw } from 'lucide-react';

interface CVPreviewModalProps {
  isOpen: boolean;
  onClose: () => void;
  data: CVData;
  templateType: 'modern' | 'classic' | 'creative';
  fileName?: string;
  onDownload?: () => void;
}

const CVPreviewModal: React.FC<CVPreviewModalProps> = ({
  isOpen,
  onClose,
  data,
  templateType,
  fileName = 'my-cv',
  onDownload
}) => {
  const [zoom, setZoom] = useState(0.7);
  const [isDownloading, setIsDownloading] = useState(false);
  const previewRef = useRef<HTMLDivElement>(null);

  if (!isOpen) return null;

  // Template component mapping
  const getTemplateComponent = () => {
    const props = { data };
    
    switch (templateType) {
      case 'modern':
        return <ModernTemplate {...props} />;
      case 'classic':
        return <ClassicTemplate {...props} />;
      case 'creative':
        return <CreativeTemplate {...props} />;
      default:
        return <ModernTemplate {...props} />;
    }
  };

  // Handle download using browser print
  const handleDownload = async () => {
    setIsDownloading(true);
    try {
      if (onDownload) {
        await onDownload();
      } else {
        // Fallback to print method
        handlePrint();
      }
    } catch (error) {
      console.error('Download failed:', error);
      alert('Download failed. Please try the print option instead.');
    } finally {
      setIsDownloading(false);
    }
  };

  // Handle print
  const handlePrint = () => {
    if (!previewRef.current) return;

    const printWindow = window.open('', '_blank');
    if (!printWindow) {
      alert('Please allow popups to print the CV');
      return;
    }

    const content = previewRef.current.innerHTML;
    const printHTML = `
      <!DOCTYPE html>
      <html>
      <head>
        <title>${fileName}-${templateType}</title>
        <style>
          @page { size: A4; margin: 0.5in; }
          body { 
            font-family: Arial, sans-serif; 
            font-size: 12px; 
            line-height: 1.4; 
            margin: 0; 
            padding: 20px;
            background: white;
          }
          * { page-break-inside: avoid !important; }
          .header { 
            background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%) !important;
            color: white !important;
            padding: 20px !important;
            margin-bottom: 20px !important;
          }
          .section { margin-bottom: 15px !important; }
          .section h2 { 
            color: #2563eb !important; 
            border-bottom: 2px solid #2563eb !important;
            padding-bottom: 4px !important;
          }
        </style>
      </head>
      <body>
        ${content}
        <script>
          window.onload = function() {
            window.print();
            setTimeout(() => window.close(), 1000);
          };
        </script>
      </body>
      </html>
    `;

    printWindow.document.write(printHTML);
    printWindow.document.close();
  };

  return (
    <div className="fixed inset-0 z-50 overflow-hidden">
      {/* Backdrop */}
      <div 
        className="absolute inset-0 bg-black bg-opacity-50 transition-opacity"
        onClick={onClose}
      />
      
      {/* Modal */}
      <div className="relative flex h-full">
        {/* Sidebar */}
        <div className="w-80 bg-white shadow-xl flex flex-col">
          {/* Header */}
          <div className="flex items-center justify-between p-4 border-b border-gray-200">
            <div>
              <h2 className="text-lg font-semibold text-gray-900">CV Preview</h2>
              <p className="text-sm text-gray-600">
                {templateType.charAt(0).toUpperCase() + templateType.slice(1)} Template
              </p>
            </div>
            <button
              onClick={onClose}
              className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-full transition-colors"
            >
              <X className="h-5 w-5" />
            </button>
          </div>

          {/* Controls */}
          <div className="p-4 border-b border-gray-200">
            <div className="space-y-4">
              {/* Zoom Controls */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Zoom Level: {Math.round(zoom * 100)}%
                </label>
                <div className="flex items-center space-x-2">
                  <button
                    onClick={() => setZoom(Math.max(0.3, zoom - 0.1))}
                    className="p-2 text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded"
                  >
                    <ZoomOut className="h-4 w-4" />
                  </button>
                  <input
                    type="range"
                    min="0.3"
                    max="1.5"
                    step="0.1"
                    value={zoom}
                    onChange={(e) => setZoom(parseFloat(e.target.value))}
                    className="flex-1"
                  />
                  <button
                    onClick={() => setZoom(Math.min(1.5, zoom + 0.1))}
                    className="p-2 text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded"
                  >
                    <ZoomIn className="h-4 w-4" />
                  </button>
                  <button
                    onClick={() => setZoom(0.7)}
                    className="p-2 text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded"
                    title="Reset zoom"
                  >
                    <RotateCcw className="h-4 w-4" />
                  </button>
                </div>
              </div>

              {/* Template Info */}
              <div className="bg-blue-50 p-3 rounded-lg">
                <h4 className="text-sm font-medium text-blue-900 mb-1">Template Features</h4>
                <ul className="text-xs text-blue-700 space-y-1">
                  {templateType === 'modern' && (
                    <>
                      <li>• Blue gradient header</li>
                      <li>• Clean modern layout</li>
                      <li>• Professional styling</li>
                    </>
                  )}
                  {templateType === 'classic' && (
                    <>
                      <li>• Traditional design</li>
                      <li>• Serif typography</li>
                      <li>• Formal layout</li>
                    </>
                  )}
                  {templateType === 'creative' && (
                    <>
                      <li>• Gradient design</li>
                      <li>• Modern elements</li>
                      <li>• Creative layout</li>
                    </>
                  )}
                </ul>
              </div>
            </div>
          </div>

          {/* Actions */}
          <div className="p-4 space-y-3">
            <button
              onClick={handleDownload}
              disabled={isDownloading}
              className="w-full inline-flex items-center justify-center px-4 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors font-medium"
            >
              {isDownloading ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Generating PDF...
                </>
              ) : (
                <>
                  <Download className="h-4 w-4 mr-2" />
                  Download PDF
                </>
              )}
            </button>

            <button
              onClick={handlePrint}
              className="w-full inline-flex items-center justify-center px-4 py-3 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors font-medium"
            >
              <Printer className="h-4 w-4 mr-2" />
              Print CV
            </button>

            <button
              onClick={onClose}
              className="w-full inline-flex items-center justify-center px-4 py-3 bg-gray-200 text-gray-800 rounded-lg hover:bg-gray-300 transition-colors font-medium"
            >
              Close Preview
            </button>
          </div>

          {/* Tips */}
          <div className="mt-auto p-4 bg-gray-50 border-t border-gray-200">
            <h4 className="text-xs font-medium text-gray-700 mb-2">💡 Tips</h4>
            <ul className="text-xs text-gray-600 space-y-1">
              <li>• Use zoom to inspect details</li>
              <li>• PDF will be optimized for A4</li>
              <li>• Content fits on single page</li>
              <li>• Print option opens browser dialog</li>
            </ul>
          </div>
        </div>

        {/* Preview Area */}
        <div className="flex-1 bg-gray-100 overflow-auto">
          <div className="p-8 flex justify-center">
            <div 
              className="bg-white shadow-2xl border border-gray-300"
              style={{
                width: '210mm',
                minHeight: '297mm',
                transform: `scale(${zoom})`,
                transformOrigin: 'top center',
                marginBottom: `${(1 - zoom) * 297}mm`
              }}
            >
              <div 
                ref={previewRef}
                style={{
                  width: '100%',
                  height: '100%',
                  padding: '20mm',
                  boxSizing: 'border-box',
                  fontSize: '12px',
                  lineHeight: '1.4',
                  fontFamily: 'Arial, sans-serif'
                }}
              >
                {getTemplateComponent()}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CVPreviewModal;
