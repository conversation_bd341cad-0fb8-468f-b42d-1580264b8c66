import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { CVData, CVFormStep, CreateCVRequest } from '../../types';
import { getEmptyCVData, getFormSteps, getNextStep, getPreviousStep, getStepIndex } from '../../utils';
import { apiService, handleApiError } from '../../services/api';
import PersonalInfoStep from './steps/PersonalInfoStep';
import ContactInfoStep from './steps/ContactInfoStep';
import EducationStep from './steps/EducationStep';
import ExperienceStep from './steps/ExperienceStep';
import SkillsStep from './steps/SkillsStep';
import ProjectsStep from './steps/ProjectsStep';
import LanguagesStep from './steps/LanguagesStep';
import HobbiesStep from './steps/HobbiesStep';
import PreviewStep from './steps/PreviewStep';
import StepIndicator from './StepIndicator';
import LoadingSpinner from '../LoadingSpinner';

interface CVBuilderWizardProps {
  initialData?: CVData;
  cvId?: number;
  onSave?: (cv: any) => void;
}

const CVBuilderWizard: React.FC<CVBuilderWizardProps> = ({ 
  initialData, 
  cvId, 
  onSave 
}) => {
  const [currentStep, setCurrentStep] = useState<CVFormStep>('personal');
  const [cvData, setCvData] = useState<CVData>(initialData || getEmptyCVData());
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [cvTitle, setCvTitle] = useState('');
  const [templateType, setTemplateType] = useState<'modern' | 'classic' | 'creative'>('modern');
  
  const navigate = useNavigate();
  const steps = getFormSteps();
  const currentStepIndex = getStepIndex(currentStep);
  const isFirst = currentStepIndex === 0;
  const isLast = currentStepIndex === steps.length - 1;

  const updateData = (step: keyof CVData, data: any) => {
    setCvData(prev => ({
      ...prev,
      [step]: data
    }));
  };

  const handleNext = () => {
    const nextStep = getNextStep(currentStep);
    if (nextStep) {
      setCurrentStep(nextStep);
    }
  };

  const handlePrevious = () => {
    const previousStep = getPreviousStep(currentStep);
    if (previousStep) {
      setCurrentStep(previousStep);
    }
  };

  const handleSave = async () => {
    if (!cvTitle.trim()) {
      setError('Please enter a title for your CV');
      return;
    }

    try {
      setIsLoading(true);
      setError('');

      const requestData: CreateCVRequest = {
        title: cvTitle,
        cv_data: cvData,
        template_type: templateType,
      };

      let response;
      if (cvId) {
        // Update existing CV
        response = await apiService.updateCV(cvId, requestData);
      } else {
        // Create new CV
        response = await apiService.createCV(requestData);
      }

      if (response.success && response.data.cv) {
        if (onSave) {
          onSave(response.data.cv);
        }
        navigate('/dashboard');
      }
    } catch (err) {
      setError(handleApiError(err));
    } finally {
      setIsLoading(false);
    }
  };

  const renderCurrentStep = () => {
    const stepProps = {
      data: cvData,
      updateData,
      onNext: handleNext,
      onPrevious: handlePrevious,
      isFirst,
      isLast,
    };

    switch (currentStep) {
      case 'personal':
        return <PersonalInfoStep {...stepProps} />;
      case 'contact':
        return <ContactInfoStep {...stepProps} />;
      case 'education':
        return <EducationStep {...stepProps} />;
      case 'experience':
        return <ExperienceStep {...stepProps} />;
      case 'skills':
        return <SkillsStep {...stepProps} />;
      case 'projects':
        return <ProjectsStep {...stepProps} />;
      case 'languages':
        return <LanguagesStep {...stepProps} />;
      case 'hobbies':
        return <HobbiesStep {...stepProps} />;
      case 'preview':
        return (
          <PreviewStep 
            {...stepProps}
            cvTitle={cvTitle}
            setCvTitle={setCvTitle}
            templateType={templateType}
            setTemplateType={setTemplateType}
            onSave={handleSave}
            isLoading={isLoading}
            error={error}
          />
        );
      default:
        return null;
    }
  };

  return (
    <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* Step Indicator */}
      <StepIndicator 
        steps={steps}
        currentStep={currentStep}
        onStepClick={setCurrentStep}
      />

      {/* Main Content */}
      <div className="mt-8">
        <div className="bg-white dark:bg-gray-800 shadow rounded-lg">
          <div className="px-6 py-8">
            {renderCurrentStep()}
          </div>
        </div>
      </div>

      {/* Navigation (for non-preview steps) */}
      {currentStep !== 'preview' && (
        <div className="mt-8 flex justify-between">
          <button
            onClick={handlePrevious}
            disabled={isFirst}
            className="btn-outline disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Previous
          </button>
          <button
            onClick={handleNext}
            className="btn-primary"
          >
            {isLast ? 'Review' : 'Next'}
          </button>
        </div>
      )}
    </div>
  );
};

export default CVBuilderWizard;
