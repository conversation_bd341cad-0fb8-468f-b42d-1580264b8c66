import React, { useState } from 'react';
import { FormStepProps } from '../../../types';
import { Heart, Plus, X } from 'lucide-react';

const HobbiesStep: React.FC<FormStepProps> = ({ data, updateData, onNext, onPrevious }) => {
  const [hobbies, setHobbies] = useState<string[]>(data.hobbies || []);
  const [newHobby, setNewHobby] = useState('');

  const suggestedHobbies = [
    'Reading', 'Photography', 'Traveling', 'Cooking', 'Gaming', 'Music',
    'Sports', 'Fitness', 'Hiking', 'Cycling', 'Swimming', 'Running',
    'Painting', 'Drawing', 'Writing', 'Blogging', 'Volunteering',
    'Gardening', 'Dancing', 'Singing', 'Playing Guitar', 'Chess',
    'Board Games', 'Movies', 'Theater', 'Learning Languages'
  ];

  const addHobby = (hobby: string) => {
    if (hobby.trim() && !hobbies.includes(hobby.trim())) {
      setHobbies([...hobbies, hobby.trim()]);
      setNewHobby('');
    }
  };

  const removeHobby = (index: number) => {
    setHobbies(hobbies.filter((_, i) => i !== index));
  };

  const handleNext = () => {
    updateData('hobbies', hobbies);
    onNext();
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      addHobby(newHobby);
    }
  };

  return (
    <div>
      <div className="flex items-center mb-6">
        <Heart className="h-6 w-6 text-primary-600 mr-3" />
        <div>
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
            Interests & Hobbies
          </h2>
          <p className="text-gray-600 dark:text-gray-400">
            Share your personal interests to give employers a glimpse of who you are
          </p>
        </div>
      </div>

      {/* Current Hobbies */}
      {hobbies.length > 0 && (
        <div className="mb-6">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-3">
            Your Interests & Hobbies
          </h3>
          <div className="flex flex-wrap gap-2">
            {hobbies.map((hobby, index) => (
              <span
                key={index}
                className="inline-flex items-center px-3 py-1 rounded-full text-sm bg-primary-100 text-primary-800 dark:bg-primary-900 dark:text-primary-200"
              >
                {hobby}
                <button
                  onClick={() => removeHobby(index)}
                  className="ml-2 text-primary-600 hover:text-primary-800"
                >
                  <X className="h-3 w-3" />
                </button>
              </span>
            ))}
          </div>
        </div>
      )}

      {/* Add Custom Hobby */}
      <div className="mb-6">
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-3">
          Add Custom Interest/Hobby
        </h3>
        <div className="flex gap-2">
          <input
            type="text"
            value={newHobby}
            onChange={(e) => setNewHobby(e.target.value)}
            onKeyPress={handleKeyPress}
            placeholder="Enter a hobby or interest"
            className="flex-1 input"
          />
          <button
            onClick={() => addHobby(newHobby)}
            disabled={!newHobby.trim() || hobbies.includes(newHobby.trim())}
            className="btn-primary disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <Plus className="h-4 w-4" />
          </button>
        </div>
      </div>

      {/* Suggested Hobbies */}
      <div className="mb-6">
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-3">
          Popular Interests & Hobbies
        </h3>
        <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">
          Click on any of these to add them to your list:
        </p>
        <div className="flex flex-wrap gap-2">
          {suggestedHobbies
            .filter(hobby => !hobbies.includes(hobby))
            .map(hobby => (
              <button
                key={hobby}
                onClick={() => addHobby(hobby)}
                className="px-3 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-100 dark:hover:bg-gray-600 hover:border-primary-500 transition-colors"
              >
                {hobby}
              </button>
            ))}
        </div>
      </div>

      {hobbies.length === 0 && (
        <div className="text-center py-8 text-gray-500 dark:text-gray-400">
          <Heart className="mx-auto h-12 w-12 mb-4 opacity-50" />
          <p>No hobbies added yet. Add some interests to personalize your CV!</p>
        </div>
      )}

      {/* Navigation */}
      <div className="flex justify-between">
        <button type="button" onClick={onPrevious} className="btn-outline">
          Previous
        </button>
        <button type="button" onClick={handleNext} className="btn-primary">
          Next: Preview
        </button>
      </div>
    </div>
  );
};

export default HobbiesStep;
