import React, { useRef, useState } from 'react';
import html2canvas from 'html2canvas';
import jsPDF from 'jspdf';
import { CVData } from '../../types';
import ModernTemplate from '../CVPreview/templates/ModernTemplate';
import ClassicTemplate from '../CVPreview/templates/ClassicTemplate';
import CreativeTemplate from '../CVPreview/templates/CreativeTemplate';
import { Download, Loader2 } from 'lucide-react';

interface CVPDFExporterJsPDFProps {
  data: CVData;
  templateType: 'modern' | 'classic' | 'creative';
  fileName?: string;
  className?: string;
}

const CVPDFExporterJsPDF: React.FC<CVPDFExporterJsPDFProps> = ({
  data,
  templateType,
  fileName = 'my-cv',
  className = ''
}) => {
  const [isExporting, setIsExporting] = useState(false);
  const [exportProgress, setExportProgress] = useState('');
  const hiddenRef = useRef<HTMLDivElement>(null);

  // Template component mapping
  const getTemplateComponent = () => {
    const props = { data };
    
    switch (templateType) {
      case 'modern':
        return <ModernTemplate {...props} />;
      case 'classic':
        return <ClassicTemplate {...props} />;
      case 'creative':
        return <CreativeTemplate {...props} />;
      default:
        return <ModernTemplate {...props} />;
    }
  };

  // Export function using jsPDF + html2canvas
  const exportToPDF = async () => {
    if (!hiddenRef.current) return;

    try {
      setIsExporting(true);
      setExportProgress('Preparing template...');

      console.log('🎯 Starting jsPDF export with template:', templateType);

      // Wait for DOM updates
      await new Promise(resolve => setTimeout(resolve, 200));

      const element = hiddenRef.current;
      
      // Make element visible for capture
      element.style.position = 'absolute';
      element.style.left = '-9999px';
      element.style.top = '0';
      element.style.visibility = 'visible';
      element.style.opacity = '1';
      element.style.transform = 'scale(1)';

      setExportProgress('Capturing template...');

      // Capture the element as canvas
      const canvas = await html2canvas(element, {
        scale: 2, // Higher resolution
        useCORS: true,
        allowTaint: true,
        backgroundColor: '#ffffff',
        width: 794, // A4 width
        height: 1123, // A4 height
        scrollX: 0,
        scrollY: 0,
        logging: false
      });

      setExportProgress('Generating PDF...');

      // A4 dimensions in mm
      const imgWidth = 210;
      const imgHeight = 297;
      
      // Calculate image dimensions to fit A4
      const canvasWidth = canvas.width;
      const canvasHeight = canvas.height;
      const ratio = canvasHeight / canvasWidth;
      
      let finalWidth = imgWidth;
      let finalHeight = imgWidth * ratio;
      
      // If height exceeds A4, scale down
      if (finalHeight > imgHeight) {
        finalHeight = imgHeight;
        finalWidth = imgHeight / ratio;
      }

      // Create PDF
      const pdf = new jsPDF({
        orientation: 'portrait',
        unit: 'mm',
        format: 'a4',
        compress: true
      });

      // Add image to PDF
      const imgData = canvas.toDataURL('image/jpeg', 0.95);
      
      // Center the image on the page
      const x = (imgWidth - finalWidth) / 2;
      const y = (imgHeight - finalHeight) / 2;
      
      pdf.addImage(imgData, 'JPEG', x, y, finalWidth, finalHeight);

      setExportProgress('Downloading...');

      // Download the PDF
      pdf.save(`${fileName}-${templateType}.pdf`);

      console.log('✅ jsPDF export completed successfully');
      setExportProgress('Complete!');

      // Reset after delay
      setTimeout(() => {
        setExportProgress('');
      }, 2000);

    } catch (error) {
      console.error('❌ jsPDF export failed:', error);
      alert('Failed to export PDF. Please try again.');
    } finally {
      setIsExporting(false);
    }
  };

  return (
    <div className={className}>
      {/* Export Button */}
      <button
        onClick={exportToPDF}
        disabled={isExporting}
        className={`
          inline-flex items-center px-4 py-2 rounded-lg font-medium transition-all duration-200
          ${isExporting 
            ? 'bg-gray-400 cursor-not-allowed' 
            : 'bg-green-600 hover:bg-green-700 active:bg-green-800'
          }
          text-white shadow-sm hover:shadow-md
        `}
      >
        {isExporting ? (
          <Loader2 className="h-4 w-4 mr-2 animate-spin" />
        ) : (
          <Download className="h-4 w-4 mr-2" />
        )}
        {isExporting ? exportProgress : 'Download PDF (jsPDF)'}
      </button>

      {/* Template Preview */}
      <div className="mt-6 border border-gray-200 rounded-lg overflow-hidden">
        <div className="bg-gray-50 px-4 py-2 border-b">
          <span className="text-sm font-medium text-gray-700">
            Preview: {templateType.charAt(0).toUpperCase() + templateType.slice(1)} Template
          </span>
        </div>
        <div className="p-4 bg-white">
          <div className="transform scale-75 origin-top-left" style={{ width: '133.33%' }}>
            {getTemplateComponent()}
          </div>
        </div>
      </div>

      {/* Hidden Template for Export */}
      <div
        ref={hiddenRef}
        style={{
          position: 'fixed',
          left: '-9999px',
          top: '0',
          width: '794px',
          height: 'auto',
          backgroundColor: '#ffffff',
          visibility: 'hidden',
          opacity: '0',
          zIndex: -1,
          fontFamily: 'Arial, sans-serif'
        }}
      >
        <div style={{ 
          width: '794px',
          minHeight: '1123px',
          padding: '30px',
          boxSizing: 'border-box'
        }}>
          {getTemplateComponent()}
        </div>
      </div>

      {/* Progress Indicator */}
      {isExporting && (
        <div className="mt-4 p-3 bg-green-50 border border-green-200 rounded-lg">
          <div className="flex items-center text-green-700">
            <Loader2 className="h-4 w-4 mr-2 animate-spin" />
            {exportProgress}
          </div>
        </div>
      )}
    </div>
  );
};

export default CVPDFExporterJsPDF;
