import axios, { AxiosInstance, AxiosResponse } from 'axios';
import {
  AuthResponse,
  LoginCredentials,
  RegisterCredentials,
  CV,
  CVResponse,
  CreateCVRequest,
  UpdateCVRequest,
  User,
  ApiError,
} from '../types';

class ApiService {
  private api: AxiosInstance;

  constructor() {
    this.api = axios.create({
      baseURL: import.meta.env.VITE_API_URL || 'http://localhost:8000/api',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
    });

    // Request interceptor to add auth token
    this.api.interceptors.request.use(
      (config) => {
        const token = localStorage.getItem('auth_token');
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );

    // Response interceptor to handle errors
    this.api.interceptors.response.use(
      (response) => response,
      (error) => {
        if (error.response?.status === 401) {
          // Token expired or invalid
          localStorage.removeItem('auth_token');
          localStorage.removeItem('user');
          window.location.href = '/login';
        }
        return Promise.reject(error);
      }
    );
  }

  // Auth endpoints
  async login(credentials: LoginCredentials): Promise<AuthResponse> {
    const response: AxiosResponse<AuthResponse> = await this.api.post('/login', credentials);
    return response.data;
  }

  async register(credentials: RegisterCredentials): Promise<AuthResponse> {
    const response: AxiosResponse<AuthResponse> = await this.api.post('/register', credentials);
    return response.data;
  }

  async logout(): Promise<void> {
    await this.api.post('/logout');
  }

  async getUser(): Promise<{ success: boolean; data: { user: User } }> {
    const response = await this.api.get('/user');
    return response.data;
  }

  // CV endpoints
  async getCVs(): Promise<CVResponse> {
    const response: AxiosResponse<CVResponse> = await this.api.get('/cvs');
    return response.data;
  }

  async getCV(id: number): Promise<CVResponse> {
    const response: AxiosResponse<CVResponse> = await this.api.get(`/cvs/${id}`);
    return response.data;
  }

  async createCV(cvData: CreateCVRequest): Promise<CVResponse> {
    const response: AxiosResponse<CVResponse> = await this.api.post('/cvs', cvData);
    return response.data;
  }

  async updateCV(id: number, cvData: UpdateCVRequest): Promise<CVResponse> {
    const response: AxiosResponse<CVResponse> = await this.api.put(`/cvs/${id}`, cvData);
    return response.data;
  }

  async deleteCV(id: number): Promise<{ success: boolean; message: string }> {
    const response = await this.api.delete(`/cvs/${id}`);
    return response.data;
  }

  async downloadCVPDF(id: number): Promise<Blob> {
    const response = await this.api.get(`/cvs/${id}/pdf`, {
      responseType: 'blob',
    });
    return response.data;
  }

  // Utility methods
  setAuthToken(token: string): void {
    localStorage.setItem('auth_token', token);
  }

  removeAuthToken(): void {
    localStorage.removeItem('auth_token');
    localStorage.removeItem('user');
  }

  getAuthToken(): string | null {
    return localStorage.getItem('auth_token');
  }

  isAuthenticated(): boolean {
    return !!this.getAuthToken();
  }
}

// Create and export a singleton instance
export const apiService = new ApiService();

// Export error handler utility
export const handleApiError = (error: any): string => {
  if (error.response?.data?.message) {
    return error.response.data.message;
  }
  
  if (error.response?.data?.errors) {
    const errors = error.response.data.errors;
    const firstError = Object.values(errors)[0] as string[];
    return firstError[0] || 'An error occurred';
  }
  
  if (error.message) {
    return error.message;
  }
  
  return 'An unexpected error occurred';
};

export default apiService;
