import React, { useState } from 'react';
import { apiService } from '../services/api';

const TemplateTestPage: React.FC = () => {
  const [selectedTemplate, setSelectedTemplate] = useState<'modern' | 'classic' | 'creative'>('modern');
  const [isLoading, setIsLoading] = useState(false);
  const [result, setResult] = useState<any>(null);
  const [error, setError] = useState<string>('');

  const templates = [
    {
      id: 'modern' as const,
      name: 'Modern',
      description: 'Clean and contemporary design with blue accents',
      preview: '🎨 Modern layout with clean lines and professional styling'
    },
    {
      id: 'classic' as const,
      name: 'Classic',
      description: 'Traditional and timeless design',
      preview: '📄 Traditional layout with serif fonts and formal styling'
    },
    {
      id: 'creative' as const,
      name: 'Creative',
      description: 'Eye-catching design with gradients and modern elements',
      preview: '✨ Creative layout with gradients and modern visual elements'
    }
  ];

  const testData = {
    personal: {
      name: 'Template Test User',
      title: 'Software Engineer',
      summary: 'Testing template selection and PDF generation functionality'
    },
    contact: {
      email: '<EMAIL>',
      phone: '555-TEST',
      location: 'Test City'
    },
    skills: {
      'Programming': ['JavaScript', 'TypeScript', 'React'],
      'Testing': ['Template Testing', 'PDF Generation']
    },
    experience: [
      {
        position: 'Template Tester',
        company: 'Test Corp',
        start_date: '2023-01',
        end_date: 'Present',
        description: 'Testing template selection and PDF generation functionality'
      }
    ],
    education: [],
    projects: [],
    languages: [],
    hobbies: ['Testing', 'Debugging']
  };

  const testTemplateFlow = async () => {
    setIsLoading(true);
    setError('');
    setResult(null);

    try {
      console.log('🧪 Starting template test flow');
      console.log('Selected template:', selectedTemplate);

      // Step 1: Create CV
      const cvData = {
        title: `Template Test - ${selectedTemplate}`,
        template_type: selectedTemplate,
        cv_data: testData
      };

      console.log('📝 Creating CV with data:', cvData);

      const createResponse = await apiService.createCV(cvData);
      console.log('✅ CV creation response:', createResponse);

      if (!createResponse.success || !createResponse.data.cv) {
        throw new Error('Failed to create CV');
      }

      const cv = createResponse.data.cv;
      console.log('📋 Created CV:', {
        id: cv.id,
        title: cv.title,
        template_type: cv.template_type
      });

      // Verify template was stored correctly
      if (cv.template_type !== selectedTemplate) {
        console.warn('⚠️ Template mismatch!', {
          requested: selectedTemplate,
          stored: cv.template_type
        });
      }

      // Step 2: Generate PDF
      console.log('📄 Generating PDF...');
      const pdfBlob = await apiService.downloadCVPDF(cv.id);
      console.log('✅ PDF generated:', {
        size: pdfBlob.size,
        type: pdfBlob.type
      });

      // Step 3: Download PDF
      const url = window.URL.createObjectURL(pdfBlob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `template-test-${selectedTemplate}.pdf`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

      setResult({
        success: true,
        cv: cv,
        pdfSize: pdfBlob.size,
        templateMatch: cv.template_type === selectedTemplate
      });

    } catch (err) {
      console.error('❌ Template test failed:', err);
      setError(err instanceof Error ? err.message : 'Unknown error');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="max-w-4xl mx-auto p-6">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">
          🧪 Template Selection Test
        </h1>

        {/* Template Selection */}
        <div className="mb-6">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
            Select Template to Test
            <span className="ml-2 text-sm text-blue-600 dark:text-blue-400">
              (Current: {selectedTemplate})
            </span>
          </h3>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {templates.map((template) => (
              <div
                key={template.id}
                className={`relative rounded-lg border-2 cursor-pointer transition-colors ${
                  selectedTemplate === template.id
                    ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
                    : 'border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600'
                }`}
                onClick={() => {
                  console.log('Template clicked:', template.id);
                  console.log('Previous template:', selectedTemplate);
                  setSelectedTemplate(template.id);
                  console.log('New template:', template.id);
                }}
              >
                <div className="p-4">
                  <div className="flex items-center justify-between mb-2">
                    <h4 className="text-lg font-medium text-gray-900 dark:text-white">
                      {template.name}
                    </h4>
                    {selectedTemplate === template.id && (
                      <div className="w-4 h-4 bg-blue-500 rounded-full flex items-center justify-center">
                        <div className="w-2 h-2 bg-white rounded-full"></div>
                      </div>
                    )}
                  </div>
                  <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">
                    {template.description}
                  </p>
                  <div className="bg-gray-100 dark:bg-gray-700 rounded p-3 text-sm text-gray-600 dark:text-gray-300">
                    {template.preview}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Test Button */}
        <div className="mb-6">
          <button
            onClick={testTemplateFlow}
            disabled={isLoading}
            className="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isLoading ? 'Testing...' : `Test ${selectedTemplate} Template`}
          </button>
        </div>

        {/* Results */}
        {error && (
          <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
            <h4 className="text-red-800 font-medium mb-2">Error:</h4>
            <p className="text-red-700">{error}</p>
          </div>
        )}

        {result && (
          <div className="mb-6 p-4 bg-green-50 border border-green-200 rounded-lg">
            <h4 className="text-green-800 font-medium mb-2">Test Results:</h4>
            <div className="space-y-2 text-sm">
              <p><strong>CV ID:</strong> {result.cv.id}</p>
              <p><strong>Requested Template:</strong> {selectedTemplate}</p>
              <p><strong>Stored Template:</strong> {result.cv.template_type}</p>
              <p><strong>Template Match:</strong> 
                <span className={result.templateMatch ? 'text-green-600' : 'text-red-600'}>
                  {result.templateMatch ? ' ✅ Correct' : ' ❌ Mismatch'}
                </span>
              </p>
              <p><strong>PDF Size:</strong> {result.pdfSize} bytes</p>
              <p><strong>PDF Downloaded:</strong> ✅ Check your downloads folder</p>
            </div>
          </div>
        )}

        {/* Instructions */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <h4 className="text-blue-800 font-medium mb-2">✅ Complete Solution Test Instructions:</h4>
          <ol className="list-decimal list-inside space-y-1 text-sm text-blue-700">
            <li><strong>Template Selection Test:</strong> Select different templates and verify they're correctly stored</li>
            <li><strong>PDF Generation Test:</strong> Click "Test Template" to generate and download PDF</li>
            <li><strong>Design Verification:</strong> Compare PDF design with browser preview</li>
            <li><strong>Single Page Test:</strong> Verify PDF fits on exactly one page</li>
            <li><strong>Cross-Template Test:</strong> Repeat with all templates to ensure differences</li>
          </ol>

          <div className="mt-4 p-3 bg-green-50 border border-green-200 rounded">
            <h5 className="text-green-800 font-medium mb-1">Expected Results:</h5>
            <ul className="text-sm text-green-700 space-y-1">
              <li>• <strong>Modern:</strong> Blue gradient header, clean design</li>
              <li>• <strong>Classic:</strong> Traditional styling, serif fonts</li>
              <li>• <strong>Creative:</strong> Gradient design, modern elements</li>
              <li>• <strong>All templates:</strong> Single page, readable text</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TemplateTestPage;
