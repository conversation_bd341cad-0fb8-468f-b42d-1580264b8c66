import React from 'react';
import { CVFormStep } from '../../types';
import { getStepIndex } from '../../utils';
import { Check } from 'lucide-react';

interface StepIndicatorProps {
  steps: { key: CVFormStep; title: string; description: string }[];
  currentStep: CVFormStep;
  onStepClick: (step: CVFormStep) => void;
}

const StepIndicator: React.FC<StepIndicatorProps> = ({
  steps,
  currentStep,
  onStepClick,
}) => {
  const currentStepIndex = getStepIndex(currentStep);

  return (
    <nav aria-label="Progress">
      <ol className="space-y-4 md:flex md:space-y-0 md:space-x-8">
        {steps.map((step, index) => {
          const isCompleted = index < currentStepIndex;
          const isCurrent = index === currentStepIndex;
          const isClickable = index <= currentStepIndex;

          return (
            <li key={step.key} className="md:flex-1">
              <button
                onClick={() => isClickable && onStepClick(step.key)}
                disabled={!isClickable}
                className={`group flex flex-col border-l-4 py-2 pl-4 md:border-l-0 md:border-t-4 md:pl-0 md:pt-4 md:pb-0 w-full text-left ${
                  isCompleted
                    ? 'border-primary-600 hover:border-primary-800'
                    : isCurrent
                    ? 'border-primary-600'
                    : 'border-gray-200 dark:border-gray-700'
                } ${isClickable ? 'cursor-pointer' : 'cursor-not-allowed'}`}
              >
                <span className="text-sm font-medium">
                  <span
                    className={`flex items-center justify-center w-8 h-8 rounded-full border-2 ${
                      isCompleted
                        ? 'bg-primary-600 border-primary-600 text-white'
                        : isCurrent
                        ? 'border-primary-600 text-primary-600'
                        : 'border-gray-300 dark:border-gray-600 text-gray-500 dark:text-gray-400'
                    }`}
                  >
                    {isCompleted ? (
                      <Check className="w-5 h-5" />
                    ) : (
                      <span>{index + 1}</span>
                    )}
                  </span>
                </span>
                <span className="mt-2">
                  <span
                    className={`text-sm font-medium ${
                      isCompleted || isCurrent
                        ? 'text-primary-600'
                        : 'text-gray-500 dark:text-gray-400'
                    }`}
                  >
                    {step.title}
                  </span>
                  <span
                    className={`text-sm ${
                      isCompleted || isCurrent
                        ? 'text-gray-700 dark:text-gray-300'
                        : 'text-gray-500 dark:text-gray-400'
                    }`}
                  >
                    {step.description}
                  </span>
                </span>
              </button>
            </li>
          );
        })}
      </ol>
    </nav>
  );
};

export default StepIndicator;
