# Template Selection Troubleshooting Guide

## Issue: PDF always uses the same template instead of the selected one

### ✅ What's Already Working

Based on my investigation, your system is correctly configured:

1. **Backend (<PERSON>vel)**:
   - ✅ Database has `template_type` column
   - ✅ CV model includes `template_type` in fillable fields
   - ✅ Controller validates and stores `template_type`
   - ✅ PDF generation uses dynamic template: `cv.templates.{$cv->template_type}`
   - ✅ All three templates exist and work correctly

2. **Frontend (React)**:
   - ✅ Template selection UI is implemented
   - ✅ Template state is managed correctly
   - ✅ Template type is sent in API requests
   - ✅ API service methods are correct

### 🔍 Debugging Steps

#### Step 1: Use the Template Debugger

I've added a `TemplateDebugger` component to your PreviewStep. To use it:

1. Go to the CV builder preview step
2. You'll see a yellow debugging panel
3. Open browser developer tools (F12) → Console tab
4. Select a template and click "Test Create CV"
5. Check the console logs for detailed request/response data

#### Step 2: Check Laravel Logs

I've added comprehensive logging to your CV controller. Check the logs:

```bash
# In your backend directory
tail -f storage/logs/laravel.log
```

Look for entries like:
- `CV Creation Request` - Shows what data is being sent
- `CV Created Successfully` - Shows what template was stored
- `Generating PDF for CV ID: X, Template: Y` - Shows what template is being used for PDF

#### Step 3: Verify Database

Check your database directly:

```sql
SELECT id, title, template_type, created_at FROM cvs ORDER BY created_at DESC LIMIT 10;
```

### 🐛 Common Issues and Solutions

#### Issue 1: Frontend not sending template_type

**Symptoms**: Laravel logs show `template_type: null` or missing

**Solution**: Check that the template selection is working in React:

```javascript
// In PreviewStep.tsx, verify this line exists:
onClick={() => setTemplateType(template.id)}

// In CVBuilderWizard.tsx, verify this line exists:
template_type: templateType,
```

#### Issue 2: Default template being used

**Symptoms**: Database always shows 'modern' template

**Solution**: Check the database migration default value:

```php
// In create_cvs_table.php
$table->string('template_type')->default('modern');
```

If the frontend isn't sending template_type, it will use the default.

#### Issue 3: Template state not updating

**Symptoms**: UI shows selected template but wrong template is sent

**Solution**: Check React state management:

```javascript
// Verify templateType state is being updated
const [templateType, setTemplateType] = useState<'modern' | 'classic' | 'creative'>('modern');
```

#### Issue 4: Caching issues

**Symptoms**: Changes not reflected immediately

**Solutions**:
```bash
# Clear Laravel caches
php artisan cache:clear
php artisan config:clear
php artisan view:clear

# Clear browser cache or use incognito mode
```

### 🔧 Quick Fixes

#### Fix 1: Ensure template_type is always sent

Add validation to ensure template_type is never empty:

```javascript
// In CVBuilderWizard.tsx, before sending request:
const requestData: CreateCVRequest = {
  title: cvTitle,
  cv_data: cvData,
  template_type: templateType || 'modern', // Fallback to modern
};
```

#### Fix 2: Add frontend validation

```javascript
// In PreviewStep.tsx, add validation:
if (!templateType) {
  setError('Please select a template');
  return;
}
```

### 🧪 Testing

Use the debugger component to test:

1. **Create new CV**: Select different templates and verify they're stored correctly
2. **Update existing CV**: Change template and verify it updates
3. **PDF generation**: Download PDFs and verify they use different layouts

### 📋 Verification Checklist

- [ ] Template selection UI is visible and clickable
- [ ] Browser console shows correct template_type in requests
- [ ] Laravel logs show correct template being stored
- [ ] Database shows correct template_type values
- [ ] Generated PDFs have different layouts/styles
- [ ] PDF file sizes are different (indicating different templates)

### 🚨 If Still Not Working

1. **Check network requests**: Use browser dev tools → Network tab to see actual API requests
2. **Verify API responses**: Check that the CV creation response includes the correct template_type
3. **Test with curl**: Use the backend test script to verify templates work independently
4. **Check for JavaScript errors**: Look for any console errors that might prevent state updates

### 📞 Need More Help?

If the issue persists:

1. Share the browser console logs from the debugger
2. Share the Laravel log entries for CV creation and PDF generation
3. Share a screenshot of the template selection UI
4. Confirm which step is failing (creation, storage, or PDF generation)

The debugging tools I've added will help identify exactly where the issue occurs.
