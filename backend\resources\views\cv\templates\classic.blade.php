<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ $cv->title }}</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Times New Roman', serif;
            line-height: 1.6;
            color: #000;
            background: #fff;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 40px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #000;
            padding-bottom: 20px;
        }
        
        .header h1 {
            font-size: 2.2em;
            color: #000;
            margin-bottom: 8px;
            text-transform: uppercase;
            letter-spacing: 2px;
        }
        
        .header .title {
            font-size: 1.1em;
            color: #333;
            margin-bottom: 15px;
            font-style: italic;
        }
        
        .contact-info {
            font-size: 0.9em;
            color: #333;
        }
        
        .contact-info span {
            margin: 0 10px;
        }
        
        .section {
            margin-bottom: 25px;
        }
        
        .section-title {
            font-size: 1.3em;
            color: #000;
            text-transform: uppercase;
            letter-spacing: 1px;
            border-bottom: 1px solid #000;
            padding-bottom: 3px;
            margin-bottom: 12px;
            font-weight: bold;
        }
        
        .summary {
            text-align: justify;
            color: #333;
            font-size: 0.95em;
        }
        
        .experience-item, .education-item, .project-item {
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 1px dotted #ccc;
        }
        
        .experience-item:last-child, .education-item:last-child, .project-item:last-child {
            border-bottom: none;
        }
        
        .item-header {
            margin-bottom: 5px;
        }
        
        .item-title {
            font-weight: bold;
            color: #000;
            font-size: 1.05em;
        }
        
        .item-company {
            color: #333;
            font-style: italic;
            margin-top: 2px;
        }
        
        .item-date {
            color: #666;
            font-size: 0.9em;
            float: right;
            margin-top: -20px;
        }
        
        .item-description {
            margin-top: 8px;
            color: #333;
            font-size: 0.95em;
            text-align: justify;
        }
        
        .skills-section {
            columns: 2;
            column-gap: 30px;
        }
        
        .skill-category {
            break-inside: avoid;
            margin-bottom: 15px;
        }
        
        .skill-category h4 {
            color: #000;
            margin-bottom: 5px;
            font-size: 1em;
            text-decoration: underline;
        }
        
        .skill-list {
            list-style: none;
            font-size: 0.9em;
        }
        
        .skill-list li {
            padding: 1px 0;
            color: #333;
        }
        
        .skill-list li:before {
            content: "• ";
            color: #000;
            font-weight: bold;
        }
        
        .languages-section, .hobbies-section {
            font-size: 0.95em;
            color: #333;
        }
        
        .language-item {
            display: inline-block;
            margin-right: 15px;
            margin-bottom: 5px;
        }
        
        .hobby-item {
            display: inline-block;
            margin-right: 10px;
            margin-bottom: 3px;
        }
        
        .hobby-item:after {
            content: " •";
            color: #666;
        }
        
        .hobby-item:last-child:after {
            content: "";
        }
        
        @media print {
            .container {
                padding: 20px;
                max-width: none;
                margin: 0;
            }

            body {
                background: white !important;
            }

            .item-date {
                position: static;
                float: none;
                display: block;
                margin-top: 2px;
            }

            /* Ensure proper page breaks */
            .section {
                page-break-inside: avoid;
            }

            .experience-item, .education-item, .project-item {
                page-break-inside: avoid;
            }

            /* Fix column layout for print */
            .skills-section {
                columns: 1;
                column-gap: 0;
            }

            .skill-category {
                break-inside: avoid;
                margin-bottom: 15px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <h1>{{ $cv->cv_data['personal']['name'] ?? 'Name' }}</h1>
            <div class="title">{{ $cv->cv_data['personal']['title'] ?? 'Professional Title' }}</div>
            <div class="contact-info">
                @if(isset($cv->cv_data['contact']['email']))
                    <span>{{ $cv->cv_data['contact']['email'] }}</span>
                @endif
                @if(isset($cv->cv_data['contact']['phone']))
                    <span>{{ $cv->cv_data['contact']['phone'] }}</span>
                @endif
                @if(isset($cv->cv_data['contact']['location']))
                    <span>{{ $cv->cv_data['contact']['location'] }}</span>
                @endif
                @if(isset($cv->cv_data['contact']['linkedin']))
                    <span>{{ $cv->cv_data['contact']['linkedin'] }}</span>
                @endif
            </div>
        </div>

        <!-- Summary -->
        @if(isset($cv->cv_data['personal']['summary']))
        <div class="section">
            <h2 class="section-title">Professional Summary</h2>
            <p class="summary">{{ $cv->cv_data['personal']['summary'] }}</p>
        </div>
        @endif

        <!-- Experience -->
        @if(isset($cv->cv_data['experience']) && count($cv->cv_data['experience']) > 0)
        <div class="section">
            <h2 class="section-title">Professional Experience</h2>
            @foreach($cv->cv_data['experience'] as $exp)
            <div class="experience-item">
                <div class="item-header">
                    <div class="item-title">{{ $exp['position'] ?? 'Position' }}</div>
                    <div class="item-company">{{ $exp['company'] ?? 'Company' }}</div>
                    <div class="item-date">{{ $exp['start_date'] ?? '' }} - {{ $exp['end_date'] ?? 'Present' }}</div>
                </div>
                @if(isset($exp['description']))
                <div class="item-description">{{ $exp['description'] }}</div>
                @endif
            </div>
            @endforeach
        </div>
        @endif

        <!-- Education -->
        @if(isset($cv->cv_data['education']) && count($cv->cv_data['education']) > 0)
        <div class="section">
            <h2 class="section-title">Education</h2>
            @foreach($cv->cv_data['education'] as $edu)
            <div class="education-item">
                <div class="item-header">
                    <div class="item-title">{{ $edu['degree'] ?? 'Degree' }}</div>
                    <div class="item-company">{{ $edu['institution'] ?? 'Institution' }}</div>
                    <div class="item-date">{{ $edu['start_date'] ?? '' }} - {{ $edu['end_date'] ?? 'Present' }}</div>
                </div>
                @if(isset($edu['description']))
                <div class="item-description">{{ $edu['description'] }}</div>
                @endif
            </div>
            @endforeach
        </div>
        @endif

        <!-- Skills -->
        @if(isset($cv->cv_data['skills']) && count($cv->cv_data['skills']) > 0)
        <div class="section">
            <h2 class="section-title">Skills & Competencies</h2>
            <div class="skills-section">
                @foreach($cv->cv_data['skills'] as $skillCategory => $skills)
                <div class="skill-category">
                    <h4>{{ ucfirst($skillCategory) }}</h4>
                    <ul class="skill-list">
                        @if(is_array($skills))
                            @foreach($skills as $skill)
                            <li>{{ $skill }}</li>
                            @endforeach
                        @else
                            <li>{{ $skills }}</li>
                        @endif
                    </ul>
                </div>
                @endforeach
            </div>
        </div>
        @endif

        <!-- Projects -->
        @if(isset($cv->cv_data['projects']) && count($cv->cv_data['projects']) > 0)
        <div class="section">
            <h2 class="section-title">Notable Projects</h2>
            @foreach($cv->cv_data['projects'] as $project)
            <div class="project-item">
                <div class="item-header">
                    <div class="item-title">{{ $project['name'] ?? 'Project Name' }}</div>
                    @if(isset($project['date']))
                    <div class="item-date">{{ $project['date'] }}</div>
                    @endif
                </div>
                @if(isset($project['description']))
                <div class="item-description">{{ $project['description'] }}</div>
                @endif
                @if(isset($project['technologies']))
                <div class="item-description"><strong>Technologies Used:</strong> {{ $project['technologies'] }}</div>
                @endif
            </div>
            @endforeach
        </div>
        @endif

        <!-- Languages -->
        @if(isset($cv->cv_data['languages']) && count($cv->cv_data['languages']) > 0)
        <div class="section">
            <h2 class="section-title">Languages</h2>
            <div class="languages-section">
                @foreach($cv->cv_data['languages'] as $language)
                <span class="language-item">
                    <strong>{{ $language['name'] ?? 'Language' }}</strong>
                    @if(isset($language['level']))
                        ({{ $language['level'] }})
                    @endif
                </span>
                @endforeach
            </div>
        </div>
        @endif

        <!-- Hobbies -->
        @if(isset($cv->cv_data['hobbies']) && count($cv->cv_data['hobbies']) > 0)
        <div class="section">
            <h2 class="section-title">Interests</h2>
            <div class="hobbies-section">
                @foreach($cv->cv_data['hobbies'] as $hobby)
                <span class="hobby-item">{{ $hobby }}</span>
                @endforeach
            </div>
        </div>
        @endif
    </div>
</body>
</html>
