# CV Generator

A modern, full-stack web application for creating professional CVs with real-time preview and PDF export functionality.

## 🚀 Features

- **User Authentication**: Secure registration and login system
- **Multi-Step CV Builder**: Step-by-step form for creating comprehensive CVs
- **Real-time Preview**: Live preview of CV as you build it
- **Multiple Templates**: Choose from professional CV templates
- **PDF Export**: Download your CV as a high-quality PDF
- **Responsive Design**: Works perfectly on desktop and mobile devices
- **Dark/Light Mode**: Toggle between themes for better user experience

## 🔧 Tech Stack

### Frontend
- **React 18** with **Vite** for fast development
- **TypeScript** for type safety
- **Tailwind CSS** for modern, responsive styling
- **React Router** for navigation
- **Axios** for API communication
- **React Hook Form** for form management

### Backend
- **<PERSON>vel** (latest stable version)
- **Laravel Sanctum** for API authentication
- **MySQL** database
- **DomPDF** for PDF generation
- **RESTful API** architecture

## 📁 Project Structure

```
cv-generator/
├── frontend/          # React + Vite frontend
│   ├── src/
│   ├── public/
│   └── package.json
├── backend/           # Laravel backend
│   ├── app/
│   ├── database/
│   ├── routes/
│   └── composer.json
└── README.md
```

## 🛠️ Installation & Setup

### Prerequisites
- Node.js (v18 or higher)
- PHP (v8.1 or higher)
- Composer
- MySQL/MariaDB

### Backend Setup
1. Navigate to the backend directory:
   ```bash
   cd backend
   ```

2. Install PHP dependencies:
   ```bash
   composer install
   ```

3. Copy environment file and configure:
   ```bash
   cp .env.example .env
   ```

4. Generate application key:
   ```bash
   php artisan key:generate
   ```

5. Configure database in `.env` file:
   ```
   DB_CONNECTION=mysql
   DB_HOST=127.0.0.1
   DB_PORT=3306
   DB_DATABASE=cv_generator
   DB_USERNAME=your_username
   DB_PASSWORD=your_password
   ```

6. Run migrations:
   ```bash
   php artisan migrate
   ```

7. Start the Laravel development server:
   ```bash
   php artisan serve
   ```

### Frontend Setup
1. Navigate to the frontend directory:
   ```bash
   cd frontend
   ```

2. Install dependencies:
   ```bash
   npm install
   ```

3. Start the development server:
   ```bash
   npm run dev
   ```

## 🗃️ Database Schema

### Users Table
- `id` (Primary Key)
- `name` (String)
- `email` (String, Unique)
- `password` (Hashed String)
- `created_at` (Timestamp)
- `updated_at` (Timestamp)

### CVs Table
- `id` (Primary Key)
- `user_id` (Foreign Key to users.id)
- `title` (String)
- `cv_data` (JSON)
- `template_type` (String)
- `created_at` (Timestamp)
- `updated_at` (Timestamp)

## 🔗 API Endpoints

### Authentication
- `POST /api/register` - User registration
- `POST /api/login` - User login
- `POST /api/logout` - User logout
- `GET /api/user` - Get authenticated user

### CV Management
- `GET /api/cvs` - Get user's CVs
- `POST /api/cvs` - Create new CV
- `GET /api/cvs/{id}` - Get specific CV
- `PUT /api/cvs/{id}` - Update CV
- `DELETE /api/cvs/{id}` - Delete CV
- `GET /api/cvs/{id}/pdf` - Download CV as PDF

## 🎨 CV Builder Steps

1. **Personal Information**: Name, title, summary, contact details
2. **Education**: Academic background and qualifications
3. **Experience**: Work history and achievements
4. **Skills**: Technical and soft skills
5. **Projects**: Portfolio projects and descriptions
6. **Languages**: Language proficiencies
7. **Hobbies**: Personal interests and activities
8. **Contact**: Additional contact information

## 📱 Responsive Design

The application is built with a mobile-first approach using Tailwind CSS, ensuring optimal user experience across all devices:
- Mobile phones (320px+)
- Tablets (768px+)
- Desktop computers (1024px+)

## 🔒 Security Features

- Password hashing using Laravel's built-in bcrypt
- CSRF protection
- API rate limiting
- Input validation and sanitization
- Secure authentication tokens

## 🚀 Deployment

### Production Build
1. Build the frontend:
   ```bash
   cd frontend && npm run build
   ```

2. Configure Laravel for production:
   ```bash
   cd backend && php artisan config:cache
   ```

## 📄 License

This project is open source and available under the [MIT License](LICENSE).

## 🤝 Contributing

Contributions are welcome! Please feel free to submit a Pull Request.

## 📞 Support

If you have any questions or need help, please open an issue on GitHub.
