import React from 'react';
import { CVData } from '../../types';
import ModernTemplate from './templates/ModernTemplate';
import ClassicTemplate from './templates/ClassicTemplate';
import CreativeTemplate from './templates/CreativeTemplate';

interface CVPreviewProps {
  data: CVData;
  template: 'modern' | 'classic' | 'creative';
  className?: string;
}

const CVPreview: React.FC<CVPreviewProps> = ({ data, template, className = '' }) => {
  const renderTemplate = () => {
    switch (template) {
      case 'modern':
        return <ModernTemplate data={data} />;
      case 'classic':
        return <ClassicTemplate data={data} />;
      case 'creative':
        return <CreativeTemplate data={data} />;
      default:
        return <ModernTemplate data={data} />;
    }
  };

  return (
    <div className={`cv-preview bg-white shadow-lg ${className}`}>
      {renderTemplate()}
    </div>
  );
};

export default CVPreview;
