import React from 'react';
import { CVData } from '../../../types';
import { Mail, Phone, MapPin, Linkedin, Globe, Github } from 'lucide-react';

interface CreativeTemplateProps {
  data: CVData;
}

const CreativeTemplate: React.FC<CreativeTemplateProps> = ({ data }) => {
  return (
    <div className="max-w-4xl mx-auto bg-white text-gray-900 font-sans">
      {/* Header with Gradient */}
      <div className="bg-gradient-to-br from-purple-600 via-blue-600 to-teal-500 text-white p-8 relative overflow-hidden">
        <div className="absolute top-0 right-0 w-32 h-32 bg-white opacity-10 rounded-full -mr-16 -mt-16"></div>
        <div className="absolute bottom-0 left-0 w-24 h-24 bg-white opacity-10 rounded-full -ml-12 -mb-12"></div>
        
        <div className="relative z-10">
          <h1 className="text-5xl font-bold mb-3 text-shadow">
            {data.personal.name || 'Your Name'}
          </h1>
          <h2 className="text-2xl font-light mb-6 opacity-90">
            {data.personal.title || 'Professional Title'}
          </h2>
          
          {/* Contact Info with Icons */}
          <div className="grid grid-cols-2 md:grid-cols-3 gap-4 text-sm">
            {data.contact.email && (
              <div className="flex items-center bg-white bg-opacity-20 rounded-lg px-3 py-2">
                <Mail className="h-4 w-4 mr-2" />
                <span className="truncate">{data.contact.email}</span>
              </div>
            )}
            {data.contact.phone && (
              <div className="flex items-center bg-white bg-opacity-20 rounded-lg px-3 py-2">
                <Phone className="h-4 w-4 mr-2" />
                {data.contact.phone}
              </div>
            )}
            {data.contact.location && (
              <div className="flex items-center bg-white bg-opacity-20 rounded-lg px-3 py-2">
                <MapPin className="h-4 w-4 mr-2" />
                {data.contact.location}
              </div>
            )}
            {data.contact.linkedin && (
              <div className="flex items-center bg-white bg-opacity-20 rounded-lg px-3 py-2">
                <Linkedin className="h-4 w-4 mr-2" />
                LinkedIn
              </div>
            )}
            {data.contact.website && (
              <div className="flex items-center bg-white bg-opacity-20 rounded-lg px-3 py-2">
                <Globe className="h-4 w-4 mr-2" />
                Website
              </div>
            )}
            {data.contact.github && (
              <div className="flex items-center bg-white bg-opacity-20 rounded-lg px-3 py-2">
                <Github className="h-4 w-4 mr-2" />
                GitHub
              </div>
            )}
          </div>
        </div>
      </div>

      <div className="p-8">
        {/* Professional Summary */}
        {data.personal.summary && (
          <section className="mb-8">
            <div className="flex items-center mb-4">
              <div className="w-4 h-4 bg-gradient-to-r from-purple-500 to-blue-500 rounded-full mr-3"></div>
              <h3 className="text-2xl font-bold text-gray-800">About Me</h3>
            </div>
            <div className="bg-gradient-to-r from-purple-50 to-blue-50 p-6 rounded-lg border-l-4 border-purple-500">
              <p className="text-gray-700 leading-relaxed italic">
                {data.personal.summary}
              </p>
            </div>
          </section>
        )}

        {/* Experience */}
        {data.experience.length > 0 && (
          <section className="mb-8">
            <div className="flex items-center mb-6">
              <div className="w-4 h-4 bg-gradient-to-r from-blue-500 to-teal-500 rounded-full mr-3"></div>
              <h3 className="text-2xl font-bold text-gray-800">Work Experience</h3>
            </div>
            <div className="space-y-6">
              {data.experience.map((exp, index) => (
                <div key={index} className="relative bg-gradient-to-r from-blue-50 to-teal-50 p-6 rounded-lg border-l-4 border-blue-500">
                  <div className="flex justify-between items-start mb-3">
                    <div>
                      <h4 className="text-xl font-bold text-gray-900">
                        {exp.position}
                      </h4>
                      <p className="text-blue-600 font-semibold text-lg">
                        {exp.company}
                      </p>
                    </div>
                    <span className="bg-gradient-to-r from-blue-500 to-teal-500 text-white px-4 py-2 rounded-full text-sm font-medium">
                      {exp.start_date} - {exp.end_date}
                    </span>
                  </div>
                  {exp.location && (
                    <p className="text-sm text-gray-600 mb-3">{exp.location}</p>
                  )}
                  {exp.description && (
                    <p className="text-gray-700 leading-relaxed">{exp.description}</p>
                  )}
                </div>
              ))}
            </div>
          </section>
        )}

        {/* Education */}
        {data.education.length > 0 && (
          <section className="mb-8">
            <div className="flex items-center mb-6">
              <div className="w-4 h-4 bg-gradient-to-r from-teal-500 to-green-500 rounded-full mr-3"></div>
              <h3 className="text-2xl font-bold text-gray-800">Education</h3>
            </div>
            <div className="space-y-4">
              {data.education.map((edu, index) => (
                <div key={index} className="bg-gradient-to-r from-teal-50 to-green-50 p-6 rounded-lg border-l-4 border-teal-500">
                  <div className="flex justify-between items-start mb-2">
                    <div>
                      <h4 className="text-lg font-bold text-gray-900">
                        {edu.degree}
                      </h4>
                      <p className="text-teal-600 font-semibold">
                        {edu.institution}
                      </p>
                    </div>
                    <span className="bg-gradient-to-r from-teal-500 to-green-500 text-white px-3 py-1 rounded-full text-sm">
                      {edu.start_date} - {edu.end_date}
                    </span>
                  </div>
                  {edu.description && (
                    <p className="text-gray-700">{edu.description}</p>
                  )}
                </div>
              ))}
            </div>
          </section>
        )}

        {/* Skills */}
        {Object.keys(data.skills).length > 0 && (
          <section className="mb-8">
            <div className="flex items-center mb-6">
              <div className="w-4 h-4 bg-gradient-to-r from-green-500 to-yellow-500 rounded-full mr-3"></div>
              <h3 className="text-2xl font-bold text-gray-800">Skills & Expertise</h3>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {Object.entries(data.skills).map(([category, skills]) => (
                <div key={category} className="bg-gradient-to-br from-green-50 to-yellow-50 p-6 rounded-lg border border-green-200 hover:shadow-lg transition-shadow">
                  <h4 className="font-bold text-gray-900 mb-3 text-lg">{category}</h4>
                  <div className="flex flex-wrap gap-2">
                    {skills.map((skill, index) => (
                      <span
                        key={index}
                        className="bg-gradient-to-r from-green-500 to-yellow-500 text-white px-3 py-1 rounded-full text-sm font-medium shadow-sm"
                      >
                        {skill}
                      </span>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          </section>
        )}

        {/* Projects */}
        {data.projects.length > 0 && (
          <section className="mb-8">
            <div className="flex items-center mb-6">
              <div className="w-4 h-4 bg-gradient-to-r from-yellow-500 to-orange-500 rounded-full mr-3"></div>
              <h3 className="text-2xl font-bold text-gray-800">Featured Projects</h3>
            </div>
            <div className="space-y-6">
              {data.projects.map((project, index) => (
                <div key={index} className="bg-gradient-to-r from-yellow-50 to-orange-50 p-6 rounded-lg border-l-4 border-yellow-500">
                  <div className="flex justify-between items-start mb-3">
                    <h4 className="text-xl font-bold text-gray-900">
                      {project.name}
                    </h4>
                    {project.date && (
                      <span className="bg-gradient-to-r from-yellow-500 to-orange-500 text-white px-3 py-1 rounded-full text-sm">
                        {project.date}
                      </span>
                    )}
                  </div>
                  {project.description && (
                    <p className="text-gray-700 mb-3">{project.description}</p>
                  )}
                  {project.technologies && (
                    <p className="text-sm text-gray-600">
                      <strong>Tech Stack:</strong> {project.technologies}
                    </p>
                  )}
                </div>
              ))}
            </div>
          </section>
        )}

        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          {/* Languages */}
          {data.languages.length > 0 && (
            <section>
              <div className="flex items-center mb-4">
                <div className="w-4 h-4 bg-gradient-to-r from-orange-500 to-red-500 rounded-full mr-3"></div>
                <h3 className="text-xl font-bold text-gray-800">Languages</h3>
              </div>
              <div className="bg-gradient-to-r from-orange-50 to-red-50 p-4 rounded-lg">
                <div className="space-y-3">
                  {data.languages.map((language, index) => (
                    <div key={index} className="flex justify-between items-center">
                      <span className="font-semibold">{language.name}</span>
                      <span className="bg-gradient-to-r from-orange-500 to-red-500 text-white px-3 py-1 rounded-full text-sm">
                        {language.level}
                      </span>
                    </div>
                  ))}
                </div>
              </div>
            </section>
          )}

          {/* Hobbies */}
          {data.hobbies.length > 0 && (
            <section>
              <div className="flex items-center mb-4">
                <div className="w-4 h-4 bg-gradient-to-r from-red-500 to-pink-500 rounded-full mr-3"></div>
                <h3 className="text-xl font-bold text-gray-800">Interests</h3>
              </div>
              <div className="bg-gradient-to-r from-red-50 to-pink-50 p-4 rounded-lg">
                <div className="flex flex-wrap gap-2">
                  {data.hobbies.map((hobby, index) => (
                    <span
                      key={index}
                      className="bg-gradient-to-r from-red-500 to-pink-500 text-white px-3 py-1 rounded-full text-sm font-medium"
                    >
                      {hobby}
                    </span>
                  ))}
                </div>
              </div>
            </section>
          )}
        </div>
      </div>
    </div>
  );
};

export default CreativeTemplate;
