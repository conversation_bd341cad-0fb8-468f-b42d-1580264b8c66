import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { FormStepProps, Language } from '../../../types';
import { generateId } from '../../../utils';
import { Globe, Plus, Trash2, Edit } from 'lucide-react';

const LanguagesStep: React.FC<FormStepProps> = ({ data, updateData, onNext, onPrevious }) => {
  const [languagesList, setLanguagesList] = useState<Language[]>(data.languages || []);
  const [editingIndex, setEditingIndex] = useState<number | null>(null);
  const [showForm, setShowForm] = useState(false);

  const {
    register,
    handleSubmit,
    reset,
    formState: { errors },
  } = useForm<Language>();

  const proficiencyLevels = ['Beginner', 'Intermediate', 'Advanced', 'Native'] as const;

  const onSubmit = (formData: Language) => {
    const newLanguage = { ...formData, id: generateId() };
    
    if (editingIndex !== null) {
      const updated = [...languagesList];
      updated[editingIndex] = newLanguage;
      setLanguagesList(updated);
      setEditingIndex(null);
    } else {
      setLanguagesList([...languagesList, newLanguage]);
    }
    
    reset();
    setShowForm(false);
  };

  const handleEdit = (index: number) => {
    const language = languagesList[index];
    reset(language);
    setEditingIndex(index);
    setShowForm(true);
  };

  const handleDelete = (index: number) => {
    const updated = languagesList.filter((_, i) => i !== index);
    setLanguagesList(updated);
  };

  const handleNext = () => {
    updateData('languages', languagesList);
    onNext();
  };

  const cancelEdit = () => {
    reset();
    setEditingIndex(null);
    setShowForm(false);
  };

  return (
    <div>
      <div className="flex items-center mb-6">
        <Globe className="h-6 w-6 text-primary-600 mr-3" />
        <div>
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
            Languages
          </h2>
          <p className="text-gray-600 dark:text-gray-400">
            Add the languages you speak and your proficiency level
          </p>
        </div>
      </div>

      {/* Languages List */}
      {languagesList.length > 0 && (
        <div className="mb-6 grid grid-cols-1 md:grid-cols-2 gap-4">
          {languagesList.map((language, index) => (
            <div key={language.id} className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
              <div className="flex justify-between items-start">
                <div className="flex-1">
                  <h3 className="font-semibold text-gray-900 dark:text-white">
                    {language.name}
                  </h3>
                  <p className="text-sm text-gray-600 dark:text-gray-300">
                    {language.level}
                  </p>
                </div>
                <div className="flex space-x-2 ml-4">
                  <button
                    onClick={() => handleEdit(index)}
                    className="p-1 text-gray-500 hover:text-primary-600"
                  >
                    <Edit className="h-4 w-4" />
                  </button>
                  <button
                    onClick={() => handleDelete(index)}
                    className="p-1 text-gray-500 hover:text-red-600"
                  >
                    <Trash2 className="h-4 w-4" />
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Add Language Button */}
      {!showForm && (
        <button
          onClick={() => setShowForm(true)}
          className="mb-6 inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600"
        >
          <Plus className="h-4 w-4 mr-2" />
          Add Language
        </button>
      )}

      {/* Language Form */}
      {showForm && (
        <form onSubmit={handleSubmit(onSubmit)} className="mb-6 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-6">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
            {editingIndex !== null ? 'Edit Language' : 'Add Language'}
          </h3>
          
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Language *
              </label>
              <input
                {...register('name', { required: 'Language name is required' })}
                type="text"
                className="mt-1 input"
                placeholder="e.g., English, Spanish, French"
              />
              {errors.name && (
                <p className="mt-1 text-sm text-red-600 dark:text-red-400">
                  {errors.name.message}
                </p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Proficiency Level *
              </label>
              <select
                {...register('level', { required: 'Proficiency level is required' })}
                className="mt-1 input"
              >
                <option value="">Select proficiency level</option>
                {proficiencyLevels.map(level => (
                  <option key={level} value={level}>
                    {level}
                  </option>
                ))}
              </select>
              {errors.level && (
                <p className="mt-1 text-sm text-red-600 dark:text-red-400">
                  {errors.level.message}
                </p>
              )}
            </div>
          </div>

          <div className="flex justify-end space-x-3 mt-6">
            <button type="button" onClick={cancelEdit} className="btn-outline">
              Cancel
            </button>
            <button type="submit" className="btn-primary">
              {editingIndex !== null ? 'Update' : 'Add'} Language
            </button>
          </div>
        </form>
      )}

      {/* Navigation */}
      <div className="flex justify-between">
        <button type="button" onClick={onPrevious} className="btn-outline">
          Previous
        </button>
        <button type="button" onClick={handleNext} className="btn-primary">
          Next: Hobbies
        </button>
      </div>
    </div>
  );
};

export default LanguagesStep;
