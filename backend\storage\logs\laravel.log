[2025-06-30 19:24:34] local.ERROR: There are no commands defined in the "sanctum" namespace. {"exception":"[object] (Symfony\\Component\\Console\\Exception\\NamespaceNotFoundException(code: 0): There are no commands defined in the \"sanctum\" namespace. at C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\symfony\\console\\Application.php:659)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\symfony\\console\\Application.php(708): Symfony\\Component\\Console\\Application->findNamespace()
#1 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\symfony\\console\\Application.php(283): Symfony\\Component\\Console\\Application->find()
#2 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun()
#3 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run()
#4 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle()
#5 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\artisan(16): Illuminate\\Foundation\\Application->handleCommand()
#6 {main}
"} 
[2025-06-30 21:16:41] local.ERROR: Trait "Laravel\Sanctum\HasApiTokens" not found {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Trait \"Laravel\\Sanctum\\HasApiTokens\" not found at C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\app\\Models\\User.php:12)
[stacktrace]
#0 {main}
"} 
[2025-06-30 21:59:00] local.ERROR: Class "finfo" not found {"exception":"[object] (Error(code: 0): Class \"finfo\" not found at C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\league\\mime-type-detection\\src\\FinfoMimeTypeDetector.php:48)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\league\\flysystem-local\\LocalFilesystemAdapter.php(86): League\\MimeTypeDetection\\FinfoMimeTypeDetector->__construct()
#1 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\VendorPublishCommand.php(295): League\\Flysystem\\Local\\LocalFilesystemAdapter->__construct()
#2 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\VendorPublishCommand.php(244): Illuminate\\Foundation\\Console\\VendorPublishCommand->publishDirectory()
#3 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\VendorPublishCommand.php(207): Illuminate\\Foundation\\Console\\VendorPublishCommand->publishItem()
#4 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\VendorPublishCommand.php(102): Illuminate\\Foundation\\Console\\VendorPublishCommand->publishTag()
#5 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Foundation\\Console\\VendorPublishCommand->handle()
#6 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()
#7 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure()
#8 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#9 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call()
#10 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(209): Illuminate\\Container\\Container->call()
#11 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute()
#12 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(178): Symfony\\Component\\Console\\Command\\Command->run()
#13 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run()
#14 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand()
#15 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun()
#16 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run()
#17 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle()
#18 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\artisan(16): Illuminate\\Foundation\\Application->handleCommand()
#19 {main}
"} 
[2025-06-30 21:59:57] local.ERROR: SQLSTATE[HY000]: General error: 1 no such table: personal_access_tokens (Connection: sqlite, SQL: insert into "personal_access_tokens" ("name", "token", "abilities", "expires_at", "tokenable_id", "tokenable_type", "updated_at", "created_at") values (auth_token, 2c22f44867edc7331b2a31e6197770e4077c992b7b10d63c47f27ec4b9c47dd4, ["*"], ?, 1, App\Models\User, 2025-06-30 21:59:57, 2025-06-30 21:59:57)) {"exception":"[object] (Illuminate\\Database\\QueryException(code: HY000): SQLSTATE[HY000]: General error: 1 no such table: personal_access_tokens (Connection: sqlite, SQL: insert into \"personal_access_tokens\" (\"name\", \"token\", \"abilities\", \"expires_at\", \"tokenable_id\", \"tokenable_type\", \"updated_at\", \"created_at\") values (auth_token, 2c22f44867edc7331b2a31e6197770e4077c992b7b10d63c47f27ec4b9c47dd4, [\"*\"], ?, 1, App\\Models\\User, 2025-06-30 21:59:57, 2025-06-30 21:59:57)) at C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:822)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback()
#1 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(557): Illuminate\\Database\\Connection->run()
#2 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(521): Illuminate\\Database\\Connection->statement()
#3 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Processors\\Processor.php(32): Illuminate\\Database\\Connection->insert()
#4 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3796): Illuminate\\Database\\Query\\Processors\\Processor->processInsertGetId()
#5 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(2205): Illuminate\\Database\\Query\\Builder->insertGetId()
#6 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1410): Illuminate\\Database\\Eloquent\\Builder->__call()
#7 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1375): Illuminate\\Database\\Eloquent\\Model->insertAndSetId()
#8 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1214): Illuminate\\Database\\Eloquent\\Model->performInsert()
#9 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\HasOneOrMany.php(370): Illuminate\\Database\\Eloquent\\Model->save()
#10 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(399): Illuminate\\Database\\Eloquent\\Relations\\HasOneOrMany->{closure:Illuminate\\Database\\Eloquent\\Relations\\HasOneOrMany::create():367}()
#11 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\HasOneOrMany.php(367): tap()
#12 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\sanctum\\src\\HasApiTokens.php(64): Illuminate\\Database\\Eloquent\\Relations\\HasOneOrMany->create()
#13 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\app\\Http\\Controllers\\Api\\AuthController.php(40): App\\Models\\User->createToken()
#14 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\Api\\AuthController->register()
#15 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch()
#16 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#17 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#18 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->{closure:Illuminate\\Routing\\Router::runRouteWithinStack():807}()
#19 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():167}()
#20 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#21 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\sanctum\\src\\Http\\Middleware\\EnsureFrontendRequestsAreStateful.php(26): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()
#22 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Laravel\\Sanctum\\Http\\Middleware\\EnsureFrontendRequestsAreStateful->{closure:Laravel\\Sanctum\\Http\\Middleware\\EnsureFrontendRequestsAreStateful::handle():25}()
#23 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():167}()
#24 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\sanctum\\src\\Http\\Middleware\\EnsureFrontendRequestsAreStateful.php(25): Illuminate\\Pipeline\\Pipeline->then()
#25 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Laravel\\Sanctum\\Http\\Middleware\\EnsureFrontendRequestsAreStateful->handle()
#26 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()
#27 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then()
#28 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack()
#29 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute()
#30 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute()
#31 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch()
#32 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():197}()
#33 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():167}()
#34 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#35 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#36 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()
#37 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#38 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#39 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()
#40 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle()
#41 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()
#42 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#43 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()
#44 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle()
#45 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()
#46 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#47 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()
#48 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle()
#49 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()
#50 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle()
#51 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()
#52 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then()
#53 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#54 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle()
#55 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest()
#56 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('...')
#57 {main}

[previous exception] [object] (PDOException(code: HY000): SQLSTATE[HY000]: General error: 1 no such table: personal_access_tokens at C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:562)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(562): PDO->prepare()
#1 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(809): Illuminate\\Database\\Connection->{closure:Illuminate\\Database\\Connection::statement():557}()
#2 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback()
#3 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(557): Illuminate\\Database\\Connection->run()
#4 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(521): Illuminate\\Database\\Connection->statement()
#5 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Processors\\Processor.php(32): Illuminate\\Database\\Connection->insert()
#6 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3796): Illuminate\\Database\\Query\\Processors\\Processor->processInsertGetId()
#7 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(2205): Illuminate\\Database\\Query\\Builder->insertGetId()
#8 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1410): Illuminate\\Database\\Eloquent\\Builder->__call()
#9 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1375): Illuminate\\Database\\Eloquent\\Model->insertAndSetId()
#10 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1214): Illuminate\\Database\\Eloquent\\Model->performInsert()
#11 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\HasOneOrMany.php(370): Illuminate\\Database\\Eloquent\\Model->save()
#12 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(399): Illuminate\\Database\\Eloquent\\Relations\\HasOneOrMany->{closure:Illuminate\\Database\\Eloquent\\Relations\\HasOneOrMany::create():367}()
#13 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\HasOneOrMany.php(367): tap()
#14 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\sanctum\\src\\HasApiTokens.php(64): Illuminate\\Database\\Eloquent\\Relations\\HasOneOrMany->create()
#15 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\app\\Http\\Controllers\\Api\\AuthController.php(40): App\\Models\\User->createToken()
#16 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\Api\\AuthController->register()
#17 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch()
#18 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#19 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#20 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->{closure:Illuminate\\Routing\\Router::runRouteWithinStack():807}()
#21 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():167}()
#22 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#23 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\sanctum\\src\\Http\\Middleware\\EnsureFrontendRequestsAreStateful.php(26): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()
#24 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Laravel\\Sanctum\\Http\\Middleware\\EnsureFrontendRequestsAreStateful->{closure:Laravel\\Sanctum\\Http\\Middleware\\EnsureFrontendRequestsAreStateful::handle():25}()
#25 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():167}()
#26 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\sanctum\\src\\Http\\Middleware\\EnsureFrontendRequestsAreStateful.php(25): Illuminate\\Pipeline\\Pipeline->then()
#27 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Laravel\\Sanctum\\Http\\Middleware\\EnsureFrontendRequestsAreStateful->handle()
#28 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()
#29 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then()
#30 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack()
#31 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute()
#32 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute()
#33 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch()
#34 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():197}()
#35 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():167}()
#36 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#37 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#38 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()
#39 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#40 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#41 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()
#42 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle()
#43 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()
#44 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#45 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()
#46 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle()
#47 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()
#48 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#49 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()
#50 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle()
#51 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()
#52 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle()
#53 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()
#54 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then()
#55 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#56 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle()
#57 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest()
#58 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('...')
#59 {main}
"} 
[2025-06-30 22:33:56] local.ERROR: SQLSTATE[HY000]: General error: 1 no such table: c_v_s (Connection: sqlite, SQL: insert into "c_v_s" ("title", "cv_data", "template_type", "user_id", "updated_at", "created_at") values (John Doe - Software Engineer, {"education":[],"contact":{"email":"<EMAIL>","location":"New York, NY","phone":"******-123-4567"},"personal":{"summary":"Experienced software engineer with 5+ years in web development","title":"Software Engineer","name":"John Doe"},"skills":[],"projects":[],"languages":[],"hobbies":[],"experience":[]}, modern, 3, 2025-06-30 22:33:56, 2025-06-30 22:33:56)) {"userId":3,"exception":"[object] (Illuminate\\Database\\QueryException(code: HY000): SQLSTATE[HY000]: General error: 1 no such table: c_v_s (Connection: sqlite, SQL: insert into \"c_v_s\" (\"title\", \"cv_data\", \"template_type\", \"user_id\", \"updated_at\", \"created_at\") values (John Doe - Software Engineer, {\"education\":[],\"contact\":{\"email\":\"<EMAIL>\",\"location\":\"New York, NY\",\"phone\":\"******-123-4567\"},\"personal\":{\"summary\":\"Experienced software engineer with 5+ years in web development\",\"title\":\"Software Engineer\",\"name\":\"John Doe\"},\"skills\":[],\"projects\":[],\"languages\":[],\"hobbies\":[],\"experience\":[]}, modern, 3, 2025-06-30 22:33:56, 2025-06-30 22:33:56)) at C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:822)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback()
#1 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(557): Illuminate\\Database\\Connection->run()
#2 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(521): Illuminate\\Database\\Connection->statement()
#3 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Processors\\Processor.php(32): Illuminate\\Database\\Connection->insert()
#4 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3796): Illuminate\\Database\\Query\\Processors\\Processor->processInsertGetId()
#5 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(2205): Illuminate\\Database\\Query\\Builder->insertGetId()
#6 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1410): Illuminate\\Database\\Eloquent\\Builder->__call()
#7 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1375): Illuminate\\Database\\Eloquent\\Model->insertAndSetId()
#8 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1214): Illuminate\\Database\\Eloquent\\Model->performInsert()
#9 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\HasOneOrMany.php(370): Illuminate\\Database\\Eloquent\\Model->save()
#10 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(399): Illuminate\\Database\\Eloquent\\Relations\\HasOneOrMany->{closure:Illuminate\\Database\\Eloquent\\Relations\\HasOneOrMany::create():367}()
#11 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\HasOneOrMany.php(367): tap()
#12 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\app\\Http\\Controllers\\Api\\CVController.php(48): Illuminate\\Database\\Eloquent\\Relations\\HasOneOrMany->create()
#13 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\Api\\CVController->store()
#14 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch()
#15 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#16 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#17 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->{closure:Illuminate\\Routing\\Router::runRouteWithinStack():807}()
#18 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():167}()
#19 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#20 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()
#21 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle()
#22 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\sanctum\\src\\Http\\Middleware\\EnsureFrontendRequestsAreStateful.php(26): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()
#23 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Laravel\\Sanctum\\Http\\Middleware\\EnsureFrontendRequestsAreStateful->{closure:Laravel\\Sanctum\\Http\\Middleware\\EnsureFrontendRequestsAreStateful::handle():25}()
#24 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():167}()
#25 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\sanctum\\src\\Http\\Middleware\\EnsureFrontendRequestsAreStateful.php(25): Illuminate\\Pipeline\\Pipeline->then()
#26 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Laravel\\Sanctum\\Http\\Middleware\\EnsureFrontendRequestsAreStateful->handle()
#27 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()
#28 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then()
#29 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack()
#30 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute()
#31 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute()
#32 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch()
#33 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():197}()
#34 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():167}()
#35 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#36 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#37 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()
#38 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#39 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#40 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()
#41 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle()
#42 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()
#43 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#44 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()
#45 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle()
#46 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()
#47 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#48 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()
#49 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle()
#50 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()
#51 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle()
#52 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()
#53 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then()
#54 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#55 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle()
#56 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest()
#57 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('...')
#58 {main}

[previous exception] [object] (PDOException(code: HY000): SQLSTATE[HY000]: General error: 1 no such table: c_v_s at C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:562)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(562): PDO->prepare()
#1 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(809): Illuminate\\Database\\Connection->{closure:Illuminate\\Database\\Connection::statement():557}()
#2 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback()
#3 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(557): Illuminate\\Database\\Connection->run()
#4 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(521): Illuminate\\Database\\Connection->statement()
#5 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Processors\\Processor.php(32): Illuminate\\Database\\Connection->insert()
#6 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3796): Illuminate\\Database\\Query\\Processors\\Processor->processInsertGetId()
#7 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(2205): Illuminate\\Database\\Query\\Builder->insertGetId()
#8 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1410): Illuminate\\Database\\Eloquent\\Builder->__call()
#9 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1375): Illuminate\\Database\\Eloquent\\Model->insertAndSetId()
#10 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1214): Illuminate\\Database\\Eloquent\\Model->performInsert()
#11 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\HasOneOrMany.php(370): Illuminate\\Database\\Eloquent\\Model->save()
#12 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(399): Illuminate\\Database\\Eloquent\\Relations\\HasOneOrMany->{closure:Illuminate\\Database\\Eloquent\\Relations\\HasOneOrMany::create():367}()
#13 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\HasOneOrMany.php(367): tap()
#14 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\app\\Http\\Controllers\\Api\\CVController.php(48): Illuminate\\Database\\Eloquent\\Relations\\HasOneOrMany->create()
#15 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\Api\\CVController->store()
#16 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch()
#17 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#18 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#19 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->{closure:Illuminate\\Routing\\Router::runRouteWithinStack():807}()
#20 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():167}()
#21 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#22 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()
#23 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle()
#24 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\sanctum\\src\\Http\\Middleware\\EnsureFrontendRequestsAreStateful.php(26): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()
#25 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Laravel\\Sanctum\\Http\\Middleware\\EnsureFrontendRequestsAreStateful->{closure:Laravel\\Sanctum\\Http\\Middleware\\EnsureFrontendRequestsAreStateful::handle():25}()
#26 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():167}()
#27 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\sanctum\\src\\Http\\Middleware\\EnsureFrontendRequestsAreStateful.php(25): Illuminate\\Pipeline\\Pipeline->then()
#28 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Laravel\\Sanctum\\Http\\Middleware\\EnsureFrontendRequestsAreStateful->handle()
#29 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()
#30 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then()
#31 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack()
#32 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute()
#33 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute()
#34 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch()
#35 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():197}()
#36 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():167}()
#37 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#38 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#39 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()
#40 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#41 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#42 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()
#43 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle()
#44 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()
#45 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#46 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()
#47 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle()
#48 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()
#49 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#50 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()
#51 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle()
#52 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()
#53 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle()
#54 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()
#55 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then()
#56 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#57 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle()
#58 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest()
#59 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('...')
#60 {main}
"} 
[2025-06-30 22:34:12] local.ERROR: SQLSTATE[HY000]: General error: 1 no such table: c_v_s (Connection: sqlite, SQL: insert into "c_v_s" ("title", "cv_data", "template_type", "user_id", "updated_at", "created_at") values (Test CV, {"personal":{"name":"John Doe","title":"Software Engineer","summary":"Test summary"},"contact":{"email":"<EMAIL>","phone":"555-1234","location":"NYC"},"education":[],"experience":[],"projects":[],"skills":[],"languages":[],"hobbies":[]}, modern, 3, 2025-06-30 22:34:12, 2025-06-30 22:34:12)) {"userId":3,"exception":"[object] (Illuminate\\Database\\QueryException(code: HY000): SQLSTATE[HY000]: General error: 1 no such table: c_v_s (Connection: sqlite, SQL: insert into \"c_v_s\" (\"title\", \"cv_data\", \"template_type\", \"user_id\", \"updated_at\", \"created_at\") values (Test CV, {\"personal\":{\"name\":\"John Doe\",\"title\":\"Software Engineer\",\"summary\":\"Test summary\"},\"contact\":{\"email\":\"<EMAIL>\",\"phone\":\"555-1234\",\"location\":\"NYC\"},\"education\":[],\"experience\":[],\"projects\":[],\"skills\":[],\"languages\":[],\"hobbies\":[]}, modern, 3, 2025-06-30 22:34:12, 2025-06-30 22:34:12)) at C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:822)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback()
#1 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(557): Illuminate\\Database\\Connection->run()
#2 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(521): Illuminate\\Database\\Connection->statement()
#3 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Processors\\Processor.php(32): Illuminate\\Database\\Connection->insert()
#4 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3796): Illuminate\\Database\\Query\\Processors\\Processor->processInsertGetId()
#5 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(2205): Illuminate\\Database\\Query\\Builder->insertGetId()
#6 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1410): Illuminate\\Database\\Eloquent\\Builder->__call()
#7 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1375): Illuminate\\Database\\Eloquent\\Model->insertAndSetId()
#8 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1214): Illuminate\\Database\\Eloquent\\Model->performInsert()
#9 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\HasOneOrMany.php(370): Illuminate\\Database\\Eloquent\\Model->save()
#10 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(399): Illuminate\\Database\\Eloquent\\Relations\\HasOneOrMany->{closure:Illuminate\\Database\\Eloquent\\Relations\\HasOneOrMany::create():367}()
#11 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\HasOneOrMany.php(367): tap()
#12 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\app\\Http\\Controllers\\Api\\CVController.php(48): Illuminate\\Database\\Eloquent\\Relations\\HasOneOrMany->create()
#13 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\Api\\CVController->store()
#14 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch()
#15 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#16 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#17 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->{closure:Illuminate\\Routing\\Router::runRouteWithinStack():807}()
#18 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():167}()
#19 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#20 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()
#21 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle()
#22 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\sanctum\\src\\Http\\Middleware\\EnsureFrontendRequestsAreStateful.php(26): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()
#23 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Laravel\\Sanctum\\Http\\Middleware\\EnsureFrontendRequestsAreStateful->{closure:Laravel\\Sanctum\\Http\\Middleware\\EnsureFrontendRequestsAreStateful::handle():25}()
#24 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():167}()
#25 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\sanctum\\src\\Http\\Middleware\\EnsureFrontendRequestsAreStateful.php(25): Illuminate\\Pipeline\\Pipeline->then()
#26 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Laravel\\Sanctum\\Http\\Middleware\\EnsureFrontendRequestsAreStateful->handle()
#27 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()
#28 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then()
#29 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack()
#30 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute()
#31 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute()
#32 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch()
#33 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():197}()
#34 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():167}()
#35 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#36 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#37 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()
#38 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#39 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#40 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()
#41 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle()
#42 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()
#43 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#44 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()
#45 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle()
#46 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()
#47 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#48 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()
#49 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle()
#50 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()
#51 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle()
#52 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()
#53 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then()
#54 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#55 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle()
#56 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest()
#57 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('...')
#58 {main}

[previous exception] [object] (PDOException(code: HY000): SQLSTATE[HY000]: General error: 1 no such table: c_v_s at C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:562)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(562): PDO->prepare()
#1 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(809): Illuminate\\Database\\Connection->{closure:Illuminate\\Database\\Connection::statement():557}()
#2 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback()
#3 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(557): Illuminate\\Database\\Connection->run()
#4 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(521): Illuminate\\Database\\Connection->statement()
#5 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Processors\\Processor.php(32): Illuminate\\Database\\Connection->insert()
#6 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3796): Illuminate\\Database\\Query\\Processors\\Processor->processInsertGetId()
#7 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(2205): Illuminate\\Database\\Query\\Builder->insertGetId()
#8 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1410): Illuminate\\Database\\Eloquent\\Builder->__call()
#9 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1375): Illuminate\\Database\\Eloquent\\Model->insertAndSetId()
#10 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1214): Illuminate\\Database\\Eloquent\\Model->performInsert()
#11 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\HasOneOrMany.php(370): Illuminate\\Database\\Eloquent\\Model->save()
#12 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(399): Illuminate\\Database\\Eloquent\\Relations\\HasOneOrMany->{closure:Illuminate\\Database\\Eloquent\\Relations\\HasOneOrMany::create():367}()
#13 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\HasOneOrMany.php(367): tap()
#14 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\app\\Http\\Controllers\\Api\\CVController.php(48): Illuminate\\Database\\Eloquent\\Relations\\HasOneOrMany->create()
#15 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\Api\\CVController->store()
#16 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch()
#17 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#18 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#19 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->{closure:Illuminate\\Routing\\Router::runRouteWithinStack():807}()
#20 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():167}()
#21 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#22 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()
#23 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle()
#24 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\sanctum\\src\\Http\\Middleware\\EnsureFrontendRequestsAreStateful.php(26): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()
#25 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Laravel\\Sanctum\\Http\\Middleware\\EnsureFrontendRequestsAreStateful->{closure:Laravel\\Sanctum\\Http\\Middleware\\EnsureFrontendRequestsAreStateful::handle():25}()
#26 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():167}()
#27 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\sanctum\\src\\Http\\Middleware\\EnsureFrontendRequestsAreStateful.php(25): Illuminate\\Pipeline\\Pipeline->then()
#28 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Laravel\\Sanctum\\Http\\Middleware\\EnsureFrontendRequestsAreStateful->handle()
#29 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()
#30 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then()
#31 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack()
#32 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute()
#33 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute()
#34 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch()
#35 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():197}()
#36 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():167}()
#37 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#38 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#39 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()
#40 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#41 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#42 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()
#43 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle()
#44 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()
#45 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#46 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()
#47 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle()
#48 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()
#49 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#50 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()
#51 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle()
#52 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()
#53 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle()
#54 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()
#55 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then()
#56 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#57 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle()
#58 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest()
#59 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('...')
#60 {main}
"} 
