[2025-06-30 19:24:34] local.ERROR: There are no commands defined in the "sanctum" namespace. {"exception":"[object] (Symfony\\Component\\Console\\Exception\\NamespaceNotFoundException(code: 0): There are no commands defined in the \"sanctum\" namespace. at C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\symfony\\console\\Application.php:659)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\symfony\\console\\Application.php(708): Symfony\\Component\\Console\\Application->findNamespace()
#1 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\symfony\\console\\Application.php(283): Symfony\\Component\\Console\\Application->find()
#2 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun()
#3 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run()
#4 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle()
#5 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\backend\\artisan(16): Illuminate\\Foundation\\Application->handleCommand()
#6 {main}
"} 
