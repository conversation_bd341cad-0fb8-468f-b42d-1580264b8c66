<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ $cv->title }}</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Arial', sans-serif;
            line-height: 1.6;
            color: #333;
            background: #fff;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 40px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 40px;
            border-bottom: 3px solid #2c3e50;
            padding-bottom: 20px;
        }
        
        .header h1 {
            font-size: 2.5em;
            color: #2c3e50;
            margin-bottom: 10px;
        }
        
        .header .title {
            font-size: 1.2em;
            color: #7f8c8d;
            margin-bottom: 15px;
        }
        
        .contact-info {
            display: flex;
            justify-content: center;
            gap: 20px;
            flex-wrap: wrap;
        }
        
        .contact-info span {
            color: #34495e;
            font-size: 0.9em;
        }
        
        .section {
            margin-bottom: 30px;
        }
        
        .section-title {
            font-size: 1.4em;
            color: #2c3e50;
            border-bottom: 2px solid #3498db;
            padding-bottom: 5px;
            margin-bottom: 15px;
        }
        
        .summary {
            font-style: italic;
            color: #555;
            text-align: justify;
        }
        
        .experience-item, .education-item, .project-item {
            margin-bottom: 20px;
            padding-left: 20px;
            border-left: 3px solid #3498db;
        }
        
        .item-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 5px;
        }
        
        .item-title {
            font-weight: bold;
            color: #2c3e50;
        }
        
        .item-company {
            color: #7f8c8d;
            font-style: italic;
        }
        
        .item-date {
            color: #95a5a6;
            font-size: 0.9em;
        }
        
        .item-description {
            margin-top: 8px;
            color: #555;
        }
        
        .skills-grid {
            /* Flexbox fallback for better PDF compatibility */
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
        }

        /* CSS Grid for modern browsers (will override flexbox) */
        @supports (display: grid) {
            .skills-grid {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            }
        }
        
        .skill-category {
            background: #ecf0f1;
            padding: 15px;
            border-radius: 5px;
            flex: 1 1 200px; /* For flexbox fallback */
            min-width: 200px;
        }
        
        .skill-category h4 {
            color: #2c3e50;
            margin-bottom: 8px;
        }
        
        .skill-list {
            list-style: none;
        }
        
        .skill-list li {
            padding: 2px 0;
            color: #555;
        }
        
        .languages, .hobbies {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
        }
        
        .language-item, .hobby-item {
            background: #3498db;
            color: white;
            padding: 5px 12px;
            border-radius: 15px;
            font-size: 0.9em;
        }
        
        @media print {
            @page {
                size: A4;
                margin: 0.5in;
            }

            body {
                background: white !important;
                font-size: 11px !important;
                line-height: 1.3 !important;
            }

            .container {
                padding: 10px !important;
                max-width: none !important;
                margin: 0 !important;
                height: auto !important;
                overflow: visible !important;
            }

            /* Force single page - prevent page breaks */
            * {
                page-break-inside: avoid !important;
                page-break-before: avoid !important;
                page-break-after: avoid !important;
            }

            .section {
                margin-bottom: 8px !important;
                page-break-inside: avoid !important;
            }

            .section h2 {
                font-size: 14px !important;
                margin-bottom: 4px !important;
            }

            .experience-item, .education-item, .project-item {
                margin-bottom: 6px !important;
                page-break-inside: avoid !important;
            }

            .header {
                margin-bottom: 10px !important;
            }

            .header h1 {
                font-size: 18px !important;
                margin-bottom: 2px !important;
            }

            .title {
                font-size: 12px !important;
                margin-bottom: 6px !important;
            }

            .contact-info {
                flex-wrap: wrap;
                justify-content: center;
                font-size: 10px !important;
                margin-bottom: 8px !important;
            }

            .contact-info span {
                margin: 2px 8px !important;
                padding: 2px 6px !important;
            }

            /* Compact skills layout for single page */
            .skills-grid {
                display: flex !important;
                flex-wrap: wrap;
                gap: 8px !important;
            }

            .skill-category {
                flex: 1 1 45% !important;
                min-width: 150px !important;
                margin-bottom: 6px !important;
                padding: 6px !important;
            }

            .skill-category h3 {
                font-size: 11px !important;
                margin-bottom: 3px !important;
            }

            .skill-list {
                font-size: 9px !important;
                line-height: 1.2 !important;
            }

            /* Compact text for single page */
            p, li, span {
                font-size: 10px !important;
                line-height: 1.2 !important;
                margin-bottom: 2px !important;
            }

            .item-date {
                font-size: 9px !important;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <h1>{{ $cv->cv_data['personal']['name'] ?? 'Name' }}</h1>
            <div class="title">{{ $cv->cv_data['personal']['title'] ?? 'Professional Title' }}</div>
            <div class="contact-info">
                @if(isset($cv->cv_data['contact']['email']))
                    <span>📧 {{ $cv->cv_data['contact']['email'] }}</span>
                @endif
                @if(isset($cv->cv_data['contact']['phone']))
                    <span>📱 {{ $cv->cv_data['contact']['phone'] }}</span>
                @endif
                @if(isset($cv->cv_data['contact']['location']))
                    <span>📍 {{ $cv->cv_data['contact']['location'] }}</span>
                @endif
                @if(isset($cv->cv_data['contact']['linkedin']))
                    <span>💼 {{ $cv->cv_data['contact']['linkedin'] }}</span>
                @endif
            </div>
        </div>

        <!-- Summary -->
        @if(isset($cv->cv_data['personal']['summary']))
        <div class="section">
            <h2 class="section-title">Professional Summary</h2>
            <p class="summary">{{ $cv->cv_data['personal']['summary'] }}</p>
        </div>
        @endif

        <!-- Experience -->
        @if(isset($cv->cv_data['experience']) && count($cv->cv_data['experience']) > 0)
        <div class="section">
            <h2 class="section-title">Work Experience</h2>
            @foreach($cv->cv_data['experience'] as $exp)
            <div class="experience-item">
                <div class="item-header">
                    <div>
                        <div class="item-title">{{ $exp['position'] ?? 'Position' }}</div>
                        <div class="item-company">{{ $exp['company'] ?? 'Company' }}</div>
                    </div>
                    <div class="item-date">{{ $exp['start_date'] ?? '' }} - {{ $exp['end_date'] ?? 'Present' }}</div>
                </div>
                @if(isset($exp['description']))
                <div class="item-description">{{ $exp['description'] }}</div>
                @endif
            </div>
            @endforeach
        </div>
        @endif

        <!-- Education -->
        @if(isset($cv->cv_data['education']) && count($cv->cv_data['education']) > 0)
        <div class="section">
            <h2 class="section-title">Education</h2>
            @foreach($cv->cv_data['education'] as $edu)
            <div class="education-item">
                <div class="item-header">
                    <div>
                        <div class="item-title">{{ $edu['degree'] ?? 'Degree' }}</div>
                        <div class="item-company">{{ $edu['institution'] ?? 'Institution' }}</div>
                    </div>
                    <div class="item-date">{{ $edu['start_date'] ?? '' }} - {{ $edu['end_date'] ?? 'Present' }}</div>
                </div>
                @if(isset($edu['description']))
                <div class="item-description">{{ $edu['description'] }}</div>
                @endif
            </div>
            @endforeach
        </div>
        @endif

        <!-- Skills -->
        @if(isset($cv->cv_data['skills']) && count($cv->cv_data['skills']) > 0)
        <div class="section">
            <h2 class="section-title">Skills</h2>
            <div class="skills-grid">
                @foreach($cv->cv_data['skills'] as $skillCategory => $skills)
                <div class="skill-category">
                    <h4>{{ ucfirst($skillCategory) }}</h4>
                    <ul class="skill-list">
                        @if(is_array($skills))
                            @foreach($skills as $skill)
                            <li>{{ $skill }}</li>
                            @endforeach
                        @else
                            <li>{{ $skills }}</li>
                        @endif
                    </ul>
                </div>
                @endforeach
            </div>
        </div>
        @endif

        <!-- Projects -->
        @if(isset($cv->cv_data['projects']) && count($cv->cv_data['projects']) > 0)
        <div class="section">
            <h2 class="section-title">Projects</h2>
            @foreach($cv->cv_data['projects'] as $project)
            <div class="project-item">
                <div class="item-header">
                    <div class="item-title">{{ $project['name'] ?? 'Project Name' }}</div>
                    @if(isset($project['date']))
                    <div class="item-date">{{ $project['date'] }}</div>
                    @endif
                </div>
                @if(isset($project['description']))
                <div class="item-description">{{ $project['description'] }}</div>
                @endif
                @if(isset($project['technologies']))
                <div class="item-description"><strong>Technologies:</strong> {{ $project['technologies'] }}</div>
                @endif
            </div>
            @endforeach
        </div>
        @endif

        <!-- Languages -->
        @if(isset($cv->cv_data['languages']) && count($cv->cv_data['languages']) > 0)
        <div class="section">
            <h2 class="section-title">Languages</h2>
            <div class="languages">
                @foreach($cv->cv_data['languages'] as $language)
                <span class="language-item">
                    {{ $language['name'] ?? 'Language' }}
                    @if(isset($language['level']))
                        - {{ $language['level'] }}
                    @endif
                </span>
                @endforeach
            </div>
        </div>
        @endif

        <!-- Hobbies -->
        @if(isset($cv->cv_data['hobbies']) && count($cv->cv_data['hobbies']) > 0)
        <div class="section">
            <h2 class="section-title">Interests & Hobbies</h2>
            <div class="hobbies">
                @foreach($cv->cv_data['hobbies'] as $hobby)
                <span class="hobby-item">{{ $hobby }}</span>
                @endforeach
            </div>
        </div>
        @endif
    </div>
</body>
</html>
