import { useRef, useState, useCallback, useEffect } from 'react';
import html2pdf from 'html2pdf.js';
import { CVData } from '../types';

interface UseTemplateAwarePDFExportProps {
  data: CVData;
  templateType: 'modern' | 'classic' | 'creative';
  fileName?: string;
}

interface UseTemplateAwarePDFExportReturn {
  isExporting: boolean;
  exportStatus: string;
  exportToPDF: () => Promise<void>;
  renderHiddenTemplate: (TemplateComponent: React.ComponentType<{ data: CVData }>) => React.ReactNode;
}

export const useTemplateAwarePDFExport = ({
  data,
  templateType,
  fileName = 'cv'
}: UseTemplateAwarePDFExportProps): UseTemplateAwarePDFExportReturn => {
  const hiddenRef = useRef<HTMLDivElement>(null);
  const [isExporting, setIsExporting] = useState(false);
  const [exportStatus, setExportStatus] = useState('');

  // Debug: Log when template changes
  useEffect(() => {
    console.log('🎨 Template changed to:', templateType);
  }, [templateType]);

  // Export function that ensures correct template is used
  const exportToPDF = useCallback(async () => {
    if (!hiddenRef.current) {
      console.error('❌ Hidden template ref not found');
      return;
    }

    try {
      setIsExporting(true);
      setExportStatus('Preparing template...');

      console.log('🎯 Starting PDF export');
      console.log('📋 Template type:', templateType);
      console.log('📄 File name:', fileName);

      // Critical: Wait for React to update the DOM with the new template
      await new Promise(resolve => setTimeout(resolve, 300));

      const element = hiddenRef.current;
      
      // Verify the element contains the expected template
      console.log('🔍 Element to export:', element);
      console.log('📏 Element dimensions:', {
        width: element.offsetWidth,
        height: element.offsetHeight
      });

      // Make element visible for html2canvas
      element.style.position = 'absolute';
      element.style.left = '-9999px';
      element.style.top = '0';
      element.style.visibility = 'visible';
      element.style.opacity = '1';
      element.style.zIndex = '9999';

      setExportStatus('Capturing template...');

      // Wait a bit more to ensure styles are applied
      await new Promise(resolve => setTimeout(resolve, 200));

      setExportStatus('Generating PDF...');

      // PDF configuration optimized for templates
      const pdfOptions = {
        margin: [0.2, 0.2, 0.2, 0.2],
        filename: `${fileName}-${templateType}.pdf`,
        image: { 
          type: 'jpeg', 
          quality: 0.98 
        },
        html2canvas: { 
          scale: 2,
          useCORS: true,
          allowTaint: true,
          backgroundColor: '#ffffff',
          scrollX: 0,
          scrollY: 0,
          width: 794, // A4 width
          height: 1123, // A4 height
          logging: true, // Enable for debugging
          onclone: (clonedDoc: Document) => {
            // Ensure styles are preserved in the clone
            console.log('📋 Document cloned for export');
            const clonedElement = clonedDoc.querySelector('.pdf-export-container');
            if (clonedElement) {
              console.log('✅ Template found in cloned document');
            } else {
              console.warn('⚠️ Template not found in cloned document');
            }
          }
        },
        jsPDF: { 
          unit: 'in', 
          format: 'a4', 
          orientation: 'portrait',
          compress: true
        },
        pagebreak: { 
          mode: ['avoid-all'],
          avoid: '.no-page-break'
        }
      };

      // Generate and download PDF
      await html2pdf()
        .set(pdfOptions)
        .from(element)
        .save();

      console.log('✅ PDF export completed successfully');
      setExportStatus('PDF downloaded!');

      // Hide element again
      element.style.position = 'fixed';
      element.style.left = '-9999px';
      element.style.visibility = 'hidden';
      element.style.opacity = '0';
      element.style.zIndex = '-1';

      // Reset status after delay
      setTimeout(() => {
        setExportStatus('');
      }, 2000);

    } catch (error) {
      console.error('❌ PDF export failed:', error);
      setExportStatus('Export failed');
      
      setTimeout(() => {
        setExportStatus('');
      }, 3000);
    } finally {
      setIsExporting(false);
    }
  }, [data, templateType, fileName]);

  // Render hidden template for export
  const renderHiddenTemplate = useCallback((TemplateComponent: React.ComponentType<{ data: CVData }>) => {
    return (
      <div
        ref={hiddenRef}
        className="pdf-export-container no-page-break"
        style={{
          position: 'fixed',
          left: '-9999px',
          top: '0',
          width: '794px', // A4 width at 96 DPI
          height: 'auto',
          backgroundColor: '#ffffff',
          visibility: 'hidden',
          opacity: '0',
          zIndex: -1,
          fontFamily: 'Arial, sans-serif',
          fontSize: '14px',
          lineHeight: '1.4',
          color: '#000000'
        }}
        data-template={templateType}
        data-export-ready="true"
      >
        <div style={{ 
          width: '794px',
          minHeight: '1123px', // A4 height at 96 DPI
          padding: '30px',
          boxSizing: 'border-box'
        }}>
          <TemplateComponent data={data} />
        </div>
      </div>
    );
  }, [data, templateType]);

  return {
    isExporting,
    exportStatus,
    exportToPDF,
    renderHiddenTemplate
  };
};

// Usage example:
/*
const MyComponent = () => {
  const [templateType, setTemplateType] = useState<'modern' | 'classic' | 'creative'>('modern');
  const [cvData, setCvData] = useState<CVData>({...});
  
  const { isExporting, exportStatus, exportToPDF, renderHiddenTemplate } = useTemplateAwarePDFExport({
    data: cvData,
    templateType,
    fileName: 'my-cv'
  });

  const getTemplateComponent = () => {
    switch (templateType) {
      case 'modern': return ModernTemplate;
      case 'classic': return ClassicTemplate;
      case 'creative': return CreativeTemplate;
      default: return ModernTemplate;
    }
  };

  return (
    <div>
      // Template selection UI
      <select value={templateType} onChange={(e) => setTemplateType(e.target.value)}>
        <option value="modern">Modern</option>
        <option value="classic">Classic</option>
        <option value="creative">Creative</option>
      </select>

      // Visible preview
      <div className="preview">
        {React.createElement(getTemplateComponent(), { data: cvData })}
      </div>

      // Export button
      <button onClick={exportToPDF} disabled={isExporting}>
        {isExporting ? exportStatus : 'Download PDF'}
      </button>

      // Hidden template for export (CRITICAL: This must be rendered)
      {renderHiddenTemplate(getTemplateComponent())}
    </div>
  );
};
*/
