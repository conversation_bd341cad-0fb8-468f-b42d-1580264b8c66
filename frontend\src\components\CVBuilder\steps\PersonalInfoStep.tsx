import React from 'react';
import { useForm } from 'react-hook-form';
import { FormStepProps, PersonalInfo } from '../../../types';
import { User } from 'lucide-react';

const PersonalInfoStep: React.FC<FormStepProps> = ({ data, updateData, onNext }) => {
  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<PersonalInfo>({
    defaultValues: data.personal,
  });

  const onSubmit = (formData: PersonalInfo) => {
    updateData('personal', formData);
    onNext();
  };

  return (
    <div>
      <div className="flex items-center mb-6">
        <User className="h-6 w-6 text-primary-600 mr-3" />
        <div>
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
            Personal Information
          </h2>
          <p className="text-gray-600 dark:text-gray-400">
            Tell us about yourself and your professional background
          </p>
        </div>
      </div>

      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
        <div>
          <label htmlFor="name" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
            Full Name *
          </label>
          <input
            {...register('name', {
              required: 'Full name is required',
              minLength: {
                value: 2,
                message: 'Name must be at least 2 characters',
              },
            })}
            type="text"
            className="mt-1 input"
            placeholder="Enter your full name"
          />
          {errors.name && (
            <p className="mt-1 text-sm text-red-600 dark:text-red-400">
              {errors.name.message}
            </p>
          )}
        </div>

        <div>
          <label htmlFor="title" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
            Professional Title *
          </label>
          <input
            {...register('title', {
              required: 'Professional title is required',
              minLength: {
                value: 2,
                message: 'Title must be at least 2 characters',
              },
            })}
            type="text"
            className="mt-1 input"
            placeholder="e.g., Software Engineer, Marketing Manager"
          />
          {errors.title && (
            <p className="mt-1 text-sm text-red-600 dark:text-red-400">
              {errors.title.message}
            </p>
          )}
        </div>

        <div>
          <label htmlFor="summary" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
            Professional Summary *
          </label>
          <textarea
            {...register('summary', {
              required: 'Professional summary is required',
              minLength: {
                value: 50,
                message: 'Summary must be at least 50 characters',
              },
              maxLength: {
                value: 500,
                message: 'Summary must not exceed 500 characters',
              },
            })}
            rows={4}
            className="mt-1 textarea"
            placeholder="Write a brief summary of your professional background, skills, and career objectives..."
          />
          {errors.summary && (
            <p className="mt-1 text-sm text-red-600 dark:text-red-400">
              {errors.summary.message}
            </p>
          )}
          <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
            Tip: Keep it concise and highlight your key strengths and achievements
          </p>
        </div>

        <div className="flex justify-end">
          <button type="submit" className="btn-primary">
            Next: Contact Information
          </button>
        </div>
      </form>
    </div>
  );
};

export default PersonalInfoStep;
