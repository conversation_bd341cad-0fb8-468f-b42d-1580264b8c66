import { useRef, useState, useCallback } from 'react';
import html2pdf from 'html2pdf.js';

interface UsePDFExportOptions {
  fileName?: string;
  quality?: number;
  scale?: number;
  format?: 'a4' | 'letter';
  orientation?: 'portrait' | 'landscape';
}

interface UsePDFExportReturn {
  exportRef: React.RefObject<HTMLDivElement>;
  isExporting: boolean;
  exportStatus: string;
  exportToPDF: () => Promise<void>;
  createHiddenExportElement: (content: React.ReactNode) => React.ReactNode;
}

export const usePDFExport = (options: UsePDFExportOptions = {}): UsePDFExportReturn => {
  const {
    fileName = 'document',
    quality = 0.95,
    scale = 2,
    format = 'a4',
    orientation = 'portrait'
  } = options;

  const exportRef = useRef<HTMLDivElement>(null);
  const [isExporting, setIsExporting] = useState(false);
  const [exportStatus, setExportStatus] = useState('');

  // PDF configuration
  const getPDFConfig = () => ({
    margin: [0.3, 0.3, 0.3, 0.3],
    filename: `${fileName}.pdf`,
    image: { 
      type: 'jpeg', 
      quality 
    },
    html2canvas: { 
      scale,
      useCORS: true,
      allowTaint: true,
      backgroundColor: '#ffffff',
      scrollX: 0,
      scrollY: 0,
      width: format === 'a4' ? 794 : 816,
      height: format === 'a4' ? 1123 : 1056,
      logging: false
    },
    jsPDF: { 
      unit: 'in', 
      format, 
      orientation,
      compress: true
    },
    pagebreak: { 
      mode: ['avoid-all', 'css', 'legacy'],
      avoid: '.no-page-break'
    }
  });

  // Export function
  const exportToPDF = useCallback(async () => {
    if (!exportRef.current) {
      console.error('Export ref is not attached to any element');
      return;
    }

    try {
      setIsExporting(true);
      setExportStatus('Preparing document...');

      console.log('🎯 Starting PDF export');

      // Wait for DOM updates
      await new Promise(resolve => setTimeout(resolve, 100));

      const element = exportRef.current;
      
      // Ensure element is visible for capture
      const originalStyle = {
        position: element.style.position,
        left: element.style.left,
        top: element.style.top,
        visibility: element.style.visibility,
        opacity: element.style.opacity
      };

      element.style.position = 'absolute';
      element.style.left = '-9999px';
      element.style.top = '0';
      element.style.visibility = 'visible';
      element.style.opacity = '1';

      setExportStatus('Generating PDF...');

      // Generate PDF
      await html2pdf()
        .set(getPDFConfig())
        .from(element)
        .save();

      // Restore original styles
      Object.assign(element.style, originalStyle);

      console.log('✅ PDF export completed');
      setExportStatus('Export completed!');

      // Reset status
      setTimeout(() => {
        setExportStatus('');
      }, 2000);

    } catch (error) {
      console.error('❌ PDF export failed:', error);
      setExportStatus('Export failed');
      setTimeout(() => {
        setExportStatus('');
      }, 3000);
    } finally {
      setIsExporting(false);
    }
  }, [fileName, quality, scale, format, orientation]);

  // Helper to create hidden export element
  const createHiddenExportElement = useCallback((content: React.ReactNode) => {
    return (
      <div
        ref={exportRef}
        style={{
          position: 'fixed',
          left: '-9999px',
          top: '0',
          width: format === 'a4' ? '794px' : '816px',
          height: 'auto',
          backgroundColor: '#ffffff',
          visibility: 'hidden',
          opacity: '0',
          zIndex: -1,
          fontFamily: 'Arial, sans-serif'
        }}
        className="pdf-export-container no-page-break"
      >
        <div style={{ 
          width: '100%',
          minHeight: format === 'a4' ? '1123px' : '1056px',
          padding: '40px',
          boxSizing: 'border-box',
          fontSize: '14px',
          lineHeight: '1.4',
          color: '#000000'
        }}>
          {content}
        </div>
      </div>
    );
  }, [format]);

  return {
    exportRef,
    isExporting,
    exportStatus,
    exportToPDF,
    createHiddenExportElement
  };
};

// Example usage component
export const PDFExportButton: React.FC<{
  children: React.ReactNode;
  fileName?: string;
  className?: string;
  disabled?: boolean;
}> = ({ children, fileName = 'document', className = '', disabled = false }) => {
  const { isExporting, exportStatus, exportToPDF } = usePDFExport({ fileName });

  return (
    <button
      onClick={exportToPDF}
      disabled={disabled || isExporting}
      className={`
        inline-flex items-center px-4 py-2 rounded-lg font-medium transition-all duration-200
        ${disabled || isExporting 
          ? 'bg-gray-400 cursor-not-allowed' 
          : 'bg-blue-600 hover:bg-blue-700 active:bg-blue-800'
        }
        text-white shadow-sm hover:shadow-md ${className}
      `}
    >
      {isExporting ? (
        <>
          <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
          {exportStatus || 'Exporting...'}
        </>
      ) : (
        children
      )}
    </button>
  );
};
