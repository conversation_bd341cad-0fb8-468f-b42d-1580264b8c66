import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { FormStepProps, Project } from '../../../types';
import { generateId } from '../../../utils';
import { FolderOpen, Plus, Trash2, Edit } from 'lucide-react';

const ProjectsStep: React.FC<FormStepProps> = ({ data, updateData, onNext, onPrevious }) => {
  const [projectsList, setProjectsList] = useState<Project[]>(data.projects || []);
  const [editingIndex, setEditingIndex] = useState<number | null>(null);
  const [showForm, setShowForm] = useState(false);

  const {
    register,
    handleSubmit,
    reset,
    formState: { errors },
  } = useForm<Project>();

  const onSubmit = (formData: Project) => {
    const newProject = { ...formData, id: generateId() };
    
    if (editingIndex !== null) {
      const updated = [...projectsList];
      updated[editingIndex] = newProject;
      setProjectsList(updated);
      setEditingIndex(null);
    } else {
      setProjectsList([...projectsList, newProject]);
    }
    
    reset();
    setShowForm(false);
  };

  const handleEdit = (index: number) => {
    const project = projectsList[index];
    reset(project);
    setEditingIndex(index);
    setShowForm(true);
  };

  const handleDelete = (index: number) => {
    const updated = projectsList.filter((_, i) => i !== index);
    setProjectsList(updated);
  };

  const handleNext = () => {
    updateData('projects', projectsList);
    onNext();
  };

  const cancelEdit = () => {
    reset();
    setEditingIndex(null);
    setShowForm(false);
  };

  return (
    <div>
      <div className="flex items-center mb-6">
        <FolderOpen className="h-6 w-6 text-primary-600 mr-3" />
        <div>
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
            Projects
          </h2>
          <p className="text-gray-600 dark:text-gray-400">
            Showcase your notable projects and achievements
          </p>
        </div>
      </div>

      {/* Projects List */}
      {projectsList.length > 0 && (
        <div className="mb-6 space-y-4">
          {projectsList.map((project, index) => (
            <div key={project.id} className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
              <div className="flex justify-between items-start">
                <div className="flex-1">
                  <h3 className="font-semibold text-gray-900 dark:text-white">
                    {project.name}
                  </h3>
                  {project.date && (
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                      {project.date}
                    </p>
                  )}
                  {project.description && (
                    <p className="mt-2 text-sm text-gray-600 dark:text-gray-300">
                      {project.description}
                    </p>
                  )}
                  {project.technologies && (
                    <p className="mt-2 text-sm text-gray-600 dark:text-gray-300">
                      <strong>Technologies:</strong> {project.technologies}
                    </p>
                  )}
                  {(project.url || project.github) && (
                    <div className="mt-2 space-x-4">
                      {project.url && (
                        <a
                          href={project.url}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-sm text-primary-600 hover:text-primary-800"
                        >
                          View Project
                        </a>
                      )}
                      {project.github && (
                        <a
                          href={project.github}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-sm text-primary-600 hover:text-primary-800"
                        >
                          GitHub
                        </a>
                      )}
                    </div>
                  )}
                </div>
                <div className="flex space-x-2 ml-4">
                  <button
                    onClick={() => handleEdit(index)}
                    className="p-2 text-gray-500 hover:text-primary-600"
                  >
                    <Edit className="h-4 w-4" />
                  </button>
                  <button
                    onClick={() => handleDelete(index)}
                    className="p-2 text-gray-500 hover:text-red-600"
                  >
                    <Trash2 className="h-4 w-4" />
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Add Project Button */}
      {!showForm && (
        <button
          onClick={() => setShowForm(true)}
          className="mb-6 inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600"
        >
          <Plus className="h-4 w-4 mr-2" />
          Add Project
        </button>
      )}

      {/* Project Form */}
      {showForm && (
        <form onSubmit={handleSubmit(onSubmit)} className="mb-6 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-6">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
            {editingIndex !== null ? 'Edit Project' : 'Add Project'}
          </h3>
          
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Project Name *
              </label>
              <input
                {...register('name', { required: 'Project name is required' })}
                type="text"
                className="mt-1 input"
                placeholder="e.g., E-commerce Website"
              />
              {errors.name && (
                <p className="mt-1 text-sm text-red-600 dark:text-red-400">
                  {errors.name.message}
                </p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Date
              </label>
              <input
                {...register('date')}
                type="month"
                className="mt-1 input"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Description *
              </label>
              <textarea
                {...register('description', { required: 'Description is required' })}
                rows={3}
                className="mt-1 textarea"
                placeholder="Describe the project, your role, and key achievements..."
              />
              {errors.description && (
                <p className="mt-1 text-sm text-red-600 dark:text-red-400">
                  {errors.description.message}
                </p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Technologies Used
              </label>
              <input
                {...register('technologies')}
                type="text"
                className="mt-1 input"
                placeholder="e.g., React, Node.js, MongoDB, AWS"
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  Project URL
                </label>
                <input
                  {...register('url')}
                  type="url"
                  className="mt-1 input"
                  placeholder="https://project-demo.com"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  GitHub Repository
                </label>
                <input
                  {...register('github')}
                  type="url"
                  className="mt-1 input"
                  placeholder="https://github.com/username/project"
                />
              </div>
            </div>
          </div>

          <div className="flex justify-end space-x-3 mt-6">
            <button type="button" onClick={cancelEdit} className="btn-outline">
              Cancel
            </button>
            <button type="submit" className="btn-primary">
              {editingIndex !== null ? 'Update' : 'Add'} Project
            </button>
          </div>
        </form>
      )}

      {/* Navigation */}
      <div className="flex justify-between">
        <button type="button" onClick={onPrevious} className="btn-outline">
          Previous
        </button>
        <button type="button" onClick={handleNext} className="btn-primary">
          Next: Languages
        </button>
      </div>
    </div>
  );
};

export default ProjectsStep;
