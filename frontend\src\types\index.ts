// User types
export interface User {
  id: number;
  name: string;
  email: string;
  created_at: string;
  updated_at: string;
}

export interface AuthResponse {
  success: boolean;
  message: string;
  data: {
    user: User;
    token: string;
    token_type: string;
  };
}

export interface LoginCredentials {
  email: string;
  password: string;
}

export interface RegisterCredentials {
  name: string;
  email: string;
  password: string;
  password_confirmation: string;
}

// CV Data types
export interface PersonalInfo {
  name: string;
  title: string;
  summary: string;
}

export interface ContactInfo {
  email: string;
  phone: string;
  location: string;
  linkedin?: string;
  website?: string;
  github?: string;
}

export interface Education {
  id?: string;
  degree: string;
  institution: string;
  start_date: string;
  end_date: string;
  description?: string;
  gpa?: string;
}

export interface Experience {
  id?: string;
  position: string;
  company: string;
  start_date: string;
  end_date: string;
  description: string;
  location?: string;
}

export interface Project {
  id?: string;
  name: string;
  description: string;
  technologies: string;
  date: string;
  url?: string;
  github?: string;
}

export interface Language {
  id?: string;
  name: string;
  level: 'Beginner' | 'Intermediate' | 'Advanced' | 'Native';
}

export interface Skills {
  [category: string]: string[];
}

export interface CVData {
  personal: PersonalInfo;
  contact: ContactInfo;
  education: Education[];
  experience: Experience[];
  projects: Project[];
  skills: Skills;
  languages: Language[];
  hobbies: string[];
}

// CV types
export interface CV {
  id: number;
  user_id: number;
  title: string;
  cv_data: CVData;
  template_type: 'modern' | 'classic' | 'creative';
  created_at: string;
  updated_at: string;
}

export interface CVResponse {
  success: boolean;
  message?: string;
  data: {
    cv?: CV;
    cvs?: CV[];
  };
}

export interface CreateCVRequest {
  title: string;
  cv_data: CVData;
  template_type: 'modern' | 'classic' | 'creative';
}

export interface UpdateCVRequest {
  title?: string;
  cv_data?: CVData;
  template_type?: 'modern' | 'classic' | 'creative';
}

// Form step types
export type CVFormStep = 
  | 'personal'
  | 'contact'
  | 'education'
  | 'experience'
  | 'skills'
  | 'projects'
  | 'languages'
  | 'hobbies'
  | 'preview';

export interface FormStepProps {
  data: CVData;
  updateData: (step: keyof CVData, data: any) => void;
  onNext: () => void;
  onPrevious: () => void;
  isFirst: boolean;
  isLast: boolean;
}

// API Error types
export interface ApiError {
  success: false;
  message: string;
  errors?: {
    [key: string]: string[];
  };
}

// Theme types
export type Theme = 'light' | 'dark';

export interface ThemeContextType {
  theme: Theme;
  toggleTheme: () => void;
}

// Auth Context types
export interface AuthContextType {
  user: User | null;
  token: string | null;
  login: (credentials: LoginCredentials) => Promise<void>;
  register: (credentials: RegisterCredentials) => Promise<void>;
  logout: () => void;
  isLoading: boolean;
  isAuthenticated: boolean;
}

// Template types
export interface Template {
  id: string;
  name: string;
  description: string;
  preview: string;
  type: 'modern' | 'classic' | 'creative';
}

// Utility types
export type Optional<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;
export type RequiredFields<T, K extends keyof T> = T & Required<Pick<T, K>>;
